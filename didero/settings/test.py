"""
Django settings for test environment.
This file contains settings specific for test runs.
"""

import os

from didero.settings.common import *  # noqa

# Flag to indicate we're in test mode (used by encryption module)
TESTING = True

# Override ENCRYPTION_KEYS for tests - django-cryptography requires valid 32-byte keys
# even though we're not actually testing encryption functionality
# This ensures tests work regardless of local environment setup
# IMPORTANT: Key must be EXACTLY 32 bytes for AES-256
ENCRYPTION_KEYS = {
    key: b"test-key-exactly-32-bytes-long00"
    for key in [
        "imap_passwords",
        "smtp_passwords",
        "google_access_tokens",
        "google_refresh_tokens",
        "google_identity_json",
        "ms_access_tokens",
        "ms_refresh_tokens",
        "email_subjects",
        "email_contents",
        "email_to",
        "email_from",
        "user_pii",
    ]
}

# Use high Redis database numbers to avoid conflicts with development
# Can be overridden with environment variables for CI/Docker
CELERY_BROKER_URL = os.environ.get("TEST_REDIS_URL", "redis://localhost:6379/15")
CELERY_RESULT_BACKEND = os.environ.get("TEST_REDIS_URL", "redis://localhost:6379/15")
CELERY_SINGLETON_BACKEND_URL = os.environ.get(
    "TEST_REDIS_URL", "redis://localhost:6379/15"
)
CELERY_ALWAYS_EAGER = True  # Tasks execute locally and synchronously

# Let Django's test framework handle database name (it adds 'test_' prefix)
# This can be overridden with TEST_DATABASE_NAME environment variable
if os.environ.get("TEST_DATABASE_NAME"):
    DATABASES["default"]["NAME"] = os.environ.get("TEST_DATABASE_NAME")

# Override database host/port for CI environments
# GitHub Actions services are accessible via localhost, not service names
if os.environ.get("CI"):
    DATABASES["default"]["HOST"] = "localhost"  # GitHub Actions uses localhost
    DATABASES["default"]["PORT"] = 5432
    CELERY_BROKER_URL = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND = "redis://localhost:6379/0"
    CELERY_SINGLETON_BACKEND_URL = "redis://localhost:6379/0"

# Use in-memory cache for tests
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
    }
}

# Use a faster password hasher for tests
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.MD5PasswordHasher",  # Much faster for tests
]

# Disable external services for test isolation
OPENSEARCH_DSL_AUTOSYNC = False  # Don't sync to OpenSearch during tests
EMAIL_BACKEND = "django.core.mail.backends.locmem.EmailBackend"  # In-memory email

# Mock AWS credentials to prevent accidental AWS calls
AWS_ACCESS_KEY_ID = "test"
AWS_SECRET_ACCESS_KEY = "test"
AWS_DEFAULT_REGION = "us-east-1"

# Disable Temporal server (use test environment)
TEMPORAL_SERVER_URL = ""

# Disable external API integrations
FEDEX_API_KEY = "test"
BROWSERBASE_API_KEY = "test"
STAGEHAND_API_KEY = "test"
