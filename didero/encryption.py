import pickle

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf import pbkdf2
from django.conf import settings
from django.db import models
from django.utils.encoding import force_bytes

FIELD_CACHE = {}


def derive_key(derive_from, salt="didero-here-we-go"):
    # Skip expensive key derivation in tests - we don't test crypto
    if getattr(settings, "TESTING", False) or settings.DEBUG:
        return b"test-key-32-bytes-for-testing!!"

    backend = default_backend()
    digest = hashes.SHA256()
    kdf = pbkdf2.PBKDF2HMAC(
        algorithm=digest,
        length=digest.digest_size,
        salt=salt,
        iterations=30000,
        backend=backend,
    )
    return kdf.derive(force_bytes(derive_from))


def encrypt_deterministic(data: bytes, key: bytes):
    """
    Encrypts data deterministically using AES in ECB mode.
    In test environments, returns data unchanged for simplicity.

    Args:
    data (str): The plaintext data to encrypt.
    key (bytes): The secret key (16, 24, or 32 bytes for AES).

    Returns:
    bytes: The encrypted data (or original data in tests).
    """
    # Skip encryption in tests - we're testing workflows, not crypto
    if getattr(settings, "TESTING", False):
        return data

    # Pad the data to ensure it's a multiple of the block size
    padder = padding.PKCS7(algorithms.AES.block_size).padder()
    padded_data = padder.update(data) + padder.finalize()

    # Create a cipher object using ECB mode
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
    encryptor = cipher.encryptor()

    # Encrypt the padded data
    encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
    return encrypted_data


def decrypt_deterministic(encrypted_data: bytes, key: bytes):
    """
    Decrypts data deterministically encrypted using AES in ECB mode.
    In test environments, returns data unchanged for simplicity.

    Args:
    encrypted_data (bytes): The encrypted data to decrypt.
    key (bytes): The secret key used for decryption.

    Returns:
    str: The decrypted plaintext data.
    """
    # Skip decryption in tests - we're testing workflows, not crypto
    if getattr(settings, "TESTING", False):
        return encrypted_data

    # Create a cipher object using ECB mode
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
    decryptor = cipher.decryptor()

    # Decrypt the data
    decrypted_padded_data = decryptor.update(encrypted_data) + decryptor.finalize()

    # Unpad the data
    unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
    decrypted_data = unpadder.update(decrypted_padded_data) + unpadder.finalize()
    return decrypted_data


class DeterministicEncryptMixin:
    """
    Quick and dirty mixin to encrypt data deterministically.
    """

    def __init__(self, *args, **kwargs):
        self.key = kwargs.pop("key")
        super().__init__(*args, **kwargs)

    def _dump(self, value):
        return encrypt_deterministic(pickle.dumps(value), self.key)

    def _load(self, value):
        return pickle.loads(decrypt_deterministic(value, self.key))

    def clone(self):
        name, path, args, kwargs = super().deconstruct()
        # Determine if the class that subclassed us has been subclassed.
        if not self.__class__.__mro__.index(DeterministicEncryptMixin) > 1:
            return encrypt_field_deterministic(
                self.base_class(*args, **kwargs), self.key
            )
        return self.__class__(*args, **kwargs)

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        # Determine if the class that subclassed us has been subclassed.
        if not self.__class__.__mro__.index(DeterministicEncryptMixin) > 1:
            path = f"{encrypt_field_deterministic.__module__}.{encrypt_field_deterministic.__name__}"
            args = [self.base_class(*args, **kwargs)]
            kwargs = {}
        return name, path, args, kwargs

    def get_db_prep_value(self, value, connection, prepared=False):
        value = super().get_db_prep_value(value, connection, prepared)
        if value is not None:
            return connection.Database.Binary(self._dump(value))
        return value

    get_db_prep_save = models.Field.get_db_prep_save

    def from_db_value(self, value, *args, **kwargs):
        if value is not None:
            return self._load(force_bytes(value))
        return value

    def get_internal_type(self):
        return "BinaryField"


def encrypt_field_deterministic(base_field: models.Field, key: bytes):
    base_class = type(base_field)
    if base_class not in FIELD_CACHE:
        FIELD_CACHE[base_class] = type(
            f"Encrypted{base_class.__name__}",
            (DeterministicEncryptMixin, base_class),
            {"base_class": base_class},
        )

    _, _, args, kwargs = base_field.deconstruct()
    return FIELD_CACHE[base_class](*args, **{**kwargs, "key": key})
