from collections import defaultdict

import structlog
from django.contrib.admin.options import get_content_type_for_model
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import IntegrityError
from django.db.models import Case, IntegerField, Q, When
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django_filters import rest_framework as filters
from django_filters.rest_framework import BaseInFilter, DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.filters import OrderingFilter
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from didero.common.utils import handle_tag_update_from_request
from didero.documents.models import Document, DocumentLink
from didero.documents.schemas import DocumentType, PriceListDetailFields
from didero.documents.serializers import (
    DocumentCommentSerializer,
    DocumentLinkSerializer,
    DocumentSerializer,
)
from didero.documents.utils import filetype_disallowed
from didero.emails.models import EmailTemplate
from didero.filters import <PERSON>Filter, ChoiceInFilter, NumberInFilter
from didero.items.models import Item, ItemCustomField
from didero.logging.documents_view_page_event import DocumentsViewPageEvent
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import (
    Supplier,
    SupplierOnboardingRequirement,
    validate_domain,
)
from didero.utils.utils import clean_website_url
from didero.views import (
    APIView,
    BaseCommentViewset,
    PagesPagination,
    TeamOwnerFilteredModelView,
)

logger = structlog.get_logger(__name__)

PARENT_OBJECT_TYPE_TO_MODEL = {
    "purchaseorder": PurchaseOrder,
    "supplieronboardingrequirement": SupplierOnboardingRequirement,
    "supplier": Supplier,
    "item": Item,
    "emailtemplate": EmailTemplate,
}


class DocumentFilter(BaseFilter):
    """
    Filter class for Document queryset with support for filtering by linked parent objects.

    Supports filtering documents by:
    - Document metadata (name, type, dates, etc.)
    - Parent object relationships (suppliers, purchase orders, items, email templates, etc.)
    - Tags and UUIDs

    All parent object filters respect team isolation and validate input parameters.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Track whether this filter requires distinct() to avoid duplicates
        self._needs_distinct = False

    # Document metadata filters
    name = filters.CharFilter(field_name="name", lookup_expr="icontains")
    doc_type = filters.ChoiceFilter(
        field_name="doc_type",
        choices=[(doc_type.value, doc_type.name) for doc_type in DocumentType],
    )
    doc_types = ChoiceInFilter(
        field_name="doc_type",
        choices=[(doc_type.value, doc_type.name) for doc_type in DocumentType],
    )
    filesize_gte = filters.NumberFilter(field_name="filesize_bytes", lookup_expr="gte")
    filesize_lte = filters.NumberFilter(field_name="filesize_bytes", lookup_expr="lte")
    created_after = filters.DateFilter(field_name="created_at", lookup_expr="gte")
    created_before = filters.DateFilter(field_name="created_at", lookup_expr="lte")
    upload_date_after = filters.DateFilter(field_name="upload_date", lookup_expr="gte")
    upload_date_before = filters.DateFilter(field_name="upload_date", lookup_expr="lte")
    # TODO: revamp how we pass filetype. should probably just be "pdf" or "png".
    # currently (look at FileTypeBadge in frontend code), it's 'application/pdf' or 'image/png'
    file_type = filters.CharFilter(field_name="content_type", lookup_expr="icontains")
    description = filters.CharFilter(field_name="description", lookup_expr="icontains")

    # Parent object filters - filter documents by their linked objects
    supplier_ids = NumberInFilter(method="filter_supplier_ids")
    purchase_order_ids = NumberInFilter(method="filter_purchase_order_ids")
    item_ids = BaseInFilter(method="filter_item_ids")  # Items use UUID IDs
    email_template_ids = NumberInFilter(method="filter_email_template_ids")
    supplier_onboarding_requirement_ids = NumberInFilter(
        method="filter_supplier_onboarding_requirement_ids"
    )

    # Tag and UUID filters
    tags = BaseInFilter(field_name="tags__name")
    uuids = BaseInFilter(field_name="uuid")
    archived = filters.BooleanFilter(method="filter_archived")

    class Meta:
        model = Document
        fields = {}

    def filter_queryset(self, queryset):
        # If request specifies archived, then we take that into account
        # Otherwise by default, we do not show documents that are archived
        if "archived" not in self.data:
            queryset = queryset.filter(archived_at__isnull=True)

        # Check if tags filter is being used with multiple values (requires distinct)
        tags_value = self.data.get("tags")
        if tags_value and isinstance(tags_value, (list, tuple)) and len(tags_value) > 1:
            self._needs_distinct = True

        return super().filter_queryset(queryset)

    def filter_archived(self, queryset, name, value):
        """Filter documents by archived status - matches other endpoint behavior"""
        if value is True:
            return queryset.filter(archived_at__isnull=False)
        elif value is False:
            return queryset.filter(archived_at__isnull=True)
        return queryset

    def filter_supplier_ids(self, queryset, name, value):
        """Filter documents by supplier IDs with validation."""
        if not value:
            return queryset

        # Filter empty/None values from the list
        valid_ids = [v for v in value if v is not None and str(v).strip()]
        if not valid_ids:
            return queryset.none()

        # Mark that distinct is needed due to JOIN with links
        self._needs_distinct = True
        return queryset.filter(
            links__parent_object_type=get_content_type_for_model(Supplier),
            links__parent_object_id__in=valid_ids,
        )

    def filter_purchase_order_ids(self, queryset, name, value):
        """Filter documents by purchase order IDs with validation."""
        if not value:
            return queryset

        # Filter empty/None values from the list
        valid_ids = [v for v in value if v is not None and str(v).strip()]
        if not valid_ids:
            return queryset.none()

        # Mark that distinct is needed due to JOIN with links
        self._needs_distinct = True
        return queryset.filter(
            links__parent_object_type=get_content_type_for_model(PurchaseOrder),
            links__parent_object_id__in=valid_ids,
        )

    def filter_item_ids(self, queryset, name, value):
        """Filter documents by item IDs with validation."""
        if not value:
            return queryset

        # Filter empty/None values and validate UUID format
        import uuid as uuid_module

        valid_uuids = []
        for v in value:
            if v is not None and str(v).strip():
                # Validate UUID format - if invalid, skip silently (following existing pattern)
                try:
                    uuid_module.UUID(str(v))
                    valid_uuids.append(str(v))
                except (ValueError, TypeError):
                    # Skip invalid UUIDs silently - consistent with existing API behavior
                    continue

        if not valid_uuids:
            return queryset.none()

        # Mark that distinct is needed due to JOIN with links
        self._needs_distinct = True
        return queryset.filter(
            links__parent_object_type=get_content_type_for_model(Item),
            links__parent_object_id__in=valid_uuids,
        )

    def filter_email_template_ids(self, queryset, name, value):
        """Filter documents by email template IDs with validation."""
        if not value:
            return queryset

        # Filter empty/None values from the list
        valid_ids = [v for v in value if v is not None and str(v).strip()]
        if not valid_ids:
            return queryset.none()

        # Mark that distinct is needed due to JOIN with links
        self._needs_distinct = True
        return queryset.filter(
            links__parent_object_type=get_content_type_for_model(EmailTemplate),
            links__parent_object_id__in=valid_ids,
        )

    def filter_supplier_onboarding_requirement_ids(self, queryset, name, value):
        """Filter documents by supplier onboarding requirement IDs with validation."""
        if not value:
            return queryset

        # Filter empty/None values from the list
        valid_ids = [v for v in value if v is not None and str(v).strip()]
        if not valid_ids:
            return queryset.none()

        # Mark that distinct is needed due to JOIN with links
        self._needs_distinct = True
        return queryset.filter(
            links__parent_object_type=get_content_type_for_model(
                SupplierOnboardingRequirement
            ),
            links__parent_object_id__in=valid_ids,
        )

    def multi_field_search(self, queryset, name, value):
        return queryset.filter(
            Q(name__icontains=value)
            | Q(description__icontains=value)
            | Q(doc_type__icontains=value)
            # Same as above: this should be exact match, but because content_type might be 'application/pdf',
            # we have to do icontains instead
            | Q(content_type__icontains=value)
            # todo search by supplier name. this is a bit tricky, since it must be via document links
            # | Q(links__parent_object_type=get_content_type_for_model(Supplier)), plus some other term
        ).distinct()


class DocumentsViewset(APIView, TeamOwnerFilteredModelView[Document]):
    model = Document
    serializer_class = DocumentSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_class = DocumentFilter
    # The fields we can sort by:
    ordering_fields = [
        "name",
        "created_at",
        "doc_type_int",
        "upload_date",
    ]
    ordering = ["-upload_date"]  # default ordering is by most recent upload
    lookup_field = "uuid"

    def filter_queryset(self, queryset):
        """Override to track whether filters require distinct() and apply it conditionally using subquery"""
        # Store the original queryset for filter processing
        needs_distinct = False

        # Check if path parameters already require distinct
        if (
            hasattr(self, "_needs_distinct_from_path")
            and self._needs_distinct_from_path
        ):
            needs_distinct = True

        # Process through each filter backend
        for backend in list(self.filter_backends):
            if isinstance(backend(), DjangoFilterBackend):
                # Create filter instance to check if distinct is needed
                filter_class = self.filterset_class
                if filter_class:
                    filter_instance = filter_class(
                        data=self.request.GET, queryset=queryset, request=self.request
                    )
                    queryset = filter_instance.qs
                    # Check if this filter requires distinct
                    if (
                        hasattr(filter_instance, "_needs_distinct")
                        and filter_instance._needs_distinct
                    ):
                        needs_distinct = True
            else:
                # Apply other filters normally
                queryset = backend().filter_queryset(self.request, queryset, self)

        # Apply distinct using subquery approach when needed (when JOINs might cause duplicates)
        if needs_distinct:
            # Get distinct IDs first (only comparing IDs, not large text/JSON fields)
            distinct_ids = queryset.values_list("id", flat=True).distinct()

            # Build fresh queryset with those IDs, preserving annotations and team filter
            # This approach ensures DISTINCT only operates on IDs, making it much faster
            docs = Document.objects.filter(id__in=distinct_ids, team=self.request.team)

            # Re-apply the doc_type_int annotation for ordering
            docs = docs.annotate(
                doc_type_int=Case(
                    When(doc_type=DocumentType.ORDER_ACKNOWLEDGEMENT, then=1),
                    When(doc_type=DocumentType.COMPLIANCE, then=2),
                    When(doc_type=DocumentType.CONTRACT, then=3),
                    When(doc_type=DocumentType.DELIVERY_CONFIRMATION, then=4),
                    When(doc_type=DocumentType.EXTERNAL_PO_IMPORT, then=5),
                    When(doc_type=DocumentType.GOODS_RECEIPT, then=6),
                    When(doc_type=DocumentType.INVOICE, then=7),
                    When(doc_type=DocumentType.NDA, then=8),
                    When(doc_type=DocumentType.PAYMENT_CONFIRMATION, then=9),
                    When(doc_type=DocumentType.PO, then=10),
                    When(doc_type=DocumentType.PRICE_LIST, then=11),
                    When(doc_type=DocumentType.PROCESSING, then=12),
                    When(doc_type=DocumentType.QUOTE_IMPORT, then=13),
                    When(doc_type=DocumentType.SHIPPING_DOCUMENT, then=14),
                    When(doc_type=DocumentType.OTHER, then=15),
                    When(doc_type=DocumentType.UNKNOWN, then=16),
                    When(doc_type=DocumentType.SHIPPING_ORDER, then=17),
                    When(doc_type=DocumentType.PICKUP_SHEET, then=18),
                    When(doc_type=DocumentType.DESPATCH_LIST, then=19),
                    When(doc_type=DocumentType.DELIVERY_NOTE, then=20),
                    output_field=IntegerField(),
                )
            )

            # Apply the default ordering to avoid pagination warning
            docs = docs.order_by(*self.ordering)

            return docs

        # Return the queryset (ordering was already applied by OrderingFilter above)
        return queryset

    def get_queryset(self):
        # Log when the page loads
        DocumentsViewPageEvent(self.request.user.pk).log_event()
        supplier_id = self.kwargs.get("supplier_id")
        purchase_order_id = self.kwargs.get("purchase_order_id")
        item_uuid = self.kwargs.get("item_uuid")

        # Track whether we need distinct() to avoid duplicates from JOINs
        self._needs_distinct_from_path = False

        try:
            docs = super().get_queryset()
            """
            add field "doc_type_int" so we can order by a customized doctype ordering
            the ordering is almost all alphabetical, so this is a bit silly. but
            because the FE displays "order acknowledgement" as just "acknowledgement",
            we want that to be first alphabetically, so we have to do all this
            """
            docs = docs.annotate(
                doc_type_int=Case(
                    When(doc_type=DocumentType.ORDER_ACKNOWLEDGEMENT, then=1),
                    When(doc_type=DocumentType.COMPLIANCE, then=2),
                    When(doc_type=DocumentType.CONTRACT, then=3),
                    When(doc_type=DocumentType.DELIVERY_CONFIRMATION, then=4),
                    When(doc_type=DocumentType.EXTERNAL_PO_IMPORT, then=5),
                    When(doc_type=DocumentType.GOODS_RECEIPT, then=6),
                    When(doc_type=DocumentType.INVOICE, then=7),
                    When(doc_type=DocumentType.NDA, then=8),
                    When(doc_type=DocumentType.PAYMENT_CONFIRMATION, then=9),
                    When(doc_type=DocumentType.PO, then=10),
                    When(doc_type=DocumentType.PRICE_LIST, then=11),
                    When(doc_type=DocumentType.PROCESSING, then=12),
                    When(doc_type=DocumentType.QUOTE_IMPORT, then=13),
                    When(doc_type=DocumentType.SHIPPING_DOCUMENT, then=14),
                    When(doc_type=DocumentType.OTHER, then=15),
                    When(doc_type=DocumentType.UNKNOWN, then=16),
                    When(doc_type=DocumentType.SHIPPING_ORDER, then=17),
                    When(doc_type=DocumentType.PICKUP_SHEET, then=18),
                    When(doc_type=DocumentType.DESPATCH_LIST, then=19),
                    When(doc_type=DocumentType.DELIVERY_NOTE, then=20),
                    output_field=IntegerField(),
                )
            )

            # TODO: is there a way to make these filters more generic?
            # TODO: add other models like items, communications whenver we add those docs/links
            if supplier_id:
                docs = docs.filter(
                    links__parent_object_type=get_content_type_for_model(Supplier),
                    links__parent_object_id=str(supplier_id),
                )
                self._needs_distinct_from_path = (
                    True  # JOIN with links table requires distinct
                )
            if purchase_order_id:
                docs = docs.filter(
                    links__parent_object_type=get_content_type_for_model(PurchaseOrder),
                    links__parent_object_id=purchase_order_id,
                )
                self._needs_distinct_from_path = (
                    True  # JOIN with links table requires distinct
                )
            if item_uuid:
                docs = docs.filter(
                    links__parent_object_type=get_content_type_for_model(Item),
                    links__parent_object_id=item_uuid,
                )
                self._needs_distinct_from_path = (
                    True  # JOIN with links table requires distinct
                )
        except DjangoValidationError as e:
            logger.error(e)
            return "no"

        # Don't apply distinct here - let filter_queryset handle it
        return docs

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a document by UUID, including archived documents.

        Overrides default behavior to allow accessing archived documents,
        consistent with other model implementations (suppliers, items, contacts).
        """
        document = get_object_or_404(
            Document, uuid=kwargs["uuid"], team=self.request.team
        )
        serializer = self.get_serializer(document)
        return Response(serializer.data)

    def retrieve_by_id(self, request, id):
        document = self.get_queryset().filter(id=id)
        if document:
            return Response(
                data=DocumentSerializer(document.get()).data,
                status=status.HTTP_200_OK,
            )
        return Response(data={}, status=status.HTTP_404_NOT_FOUND)

    def update(self, request, *args, **kwargs):
        """
        Update a document, including archived documents (for unarchiving).

        Overrides default behavior to allow updating archived documents,
        enabling the unarchive workflow via PATCH requests with archived_at: null.
        """
        document = get_object_or_404(
            Document, uuid=kwargs["uuid"], team=self.request.team
        )
        tag_update_success = handle_tag_update_from_request(document, request)
        if not tag_update_success:
            return self.single_error_response(
                detail="Tag does not exist for this team.",
                attr="tags",
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(document, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)

    def perform_create(self, serializer):
        assert serializer.is_valid()
        # TODO: how can we move these validations to the create method
        # so we can return them to the FE/API
        fileobj = serializer.validated_data.get("document")
        doc_type = serializer.validated_data.get(
            "doc_type", DocumentType.PROCESSING.value
        )
        if not fileobj:
            raise ValidationError(
                "There was no document sent as part of the create call."
            )

        # fily extension check
        if filetype_disallowed(fileobj.name):
            raise ValidationError(
                f"The document extension is not allowed [{fileobj.name}]"
            )

        doc = Document.get_or_create_from_file(
            fileobj, self.request.team, doc_type=doc_type
        )

        serializer.instance = doc
        return serializer.instance

    # This method catches the perform_create errors and handles them
    # perform_create does not return anything so returning an error response
    # these would do nothing
    def create(self, validated_data, supplier_id=None, order_id=None, item_uuid=None):
        try:
            return super().create(validated_data)
        except ValidationError as exc:
            return self.single_error_response(
                detail=exc.args[0],
                attr="document",
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as exc:
            raise exc

    def perform_update(self, serializer):
        assert serializer.is_valid()
        doc = serializer.instance
        fileobj = serializer.validated_data.get("document")
        if fileobj:
            doc.update_document(fileobj)
        serializer.save()
        return doc

    @action(detail=True, methods=["put"])
    def update_supplier(self, request, uuid):
        document = self.get_object()
        supplier_id = request.data.get("supplier_id")

        if not supplier_id:
            return self.single_error_response(
                detail="supplier_id is required",
                attr="supplier_id",
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if supplier exists
        supplier = Supplier.objects.filter(id=supplier_id, team=request.team).first()
        if not supplier:
            return self.single_error_response(
                detail="Supplier not found",
                attr="supplier_id",
                status=status.HTTP_404_NOT_FOUND,
            )

        # Remove existing supplier links for this document
        DocumentLink.objects.filter(
            document=document, parent_object_type=get_content_type_for_model(Supplier)
        ).delete()

        # Create new supplier link
        DocumentLink.objects.create(
            document=document,
            parent_object_type=get_content_type_for_model(Supplier),
            parent_object_id=supplier.id,
        )

        # Return updated document
        serializer = self.get_serializer(document)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def create_items_from_price_list(self, request, uuid):
        team = request.team
        doc = self.get_object()
        if not doc.doc_type == DocumentType.PRICE_LIST:
            return self.single_error_response(
                detail="This endpoint is only for price lists.",
                attr="docType",
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            # get the parsed details
            parsed_details = doc.parsed_document_details
            if not parsed_details:
                raise ValidationError("No parsed details found for this document.")
            # convert back to the PriceListDetailFields schema
            parsed_details = PriceListDetailFields(**parsed_details)
        except ValidationError as e:
            logger.error(f"Error parsing parsed details: {e}", team)
            return self.single_error_response(
                detail=str(e),
                attr="parsedDetails",
                status=status.HTTP_400_BAD_REQUEST,
            )
        # get the supplier
        supplier_name = parsed_details.supplier_name
        if not supplier_name:
            return self.single_error_response(
                detail="No supplier name found in parsed details.",
                attr="parsedDetails",
                status=status.HTTP_400_BAD_REQUEST,
            )

        website_url = parsed_details.supplier_website_url

        if website_url:
            website_url = clean_website_url(website_url)
        try:
            validate_domain(website_url)
        except (DjangoValidationError, ValidationError) as e:
            logger.info(
                f"invalid website: {website_url}. attempting to create supplier with no domain",
                team_id=team.pk,
                document_id=doc.id,
                exc=e,
            )
            website_url = None

        try:
            supplier, created = Supplier.objects.update_or_create(
                name=supplier_name,
                team=team,
                defaults={"website_url": website_url},
            )
        except Exception:
            return self.single_error_response(
                detail="Error during supplier creation",
                attr="supplier",
                status=status.HTTP_400_BAD_REQUEST,
            )
        if created:
            logger.info(
                "Created supplier for team",
                team_id=team.pk,
                supplier_id=supplier.pk,
                document_id=doc.id,
            )
        else:
            logger.info(
                "Found existing supplier for team",
                team_id=team.pk,
                supplier_id=supplier.pk,
                document_id=doc.id,
            )
        # items is a list of ItemCustomFieldsModel
        parsed_items = parsed_details.items
        logger.info(
            f"Found {len(parsed_items)} items in parsed details.",
            team_id=team.pk,
            supplier_id=supplier.pk,
            document_id=doc.id,
        )
        results = defaultdict(list)
        for parsed_item in parsed_items:
            try:
                # create the items; set the defaults to the item fields
                # but exclude the supplier and remove keys that have empty values (eg: "", None)
                item, created = Item.objects.update_or_create(
                    supplier=supplier,
                    team=team,
                    item_number=parsed_item.item.item_number,
                    defaults={
                        k: v
                        for k, v in parsed_item.item.model_dump(
                            exclude=["supplier"]
                        ).items()
                        if v
                    },
                )
                if created:
                    results["new_items"].append(item.item_number)
                else:
                    results["existing_items"].append(item.item_number)
                logger.info(
                    "Created " if created else "Updated " + "item for supplier",
                    team_id=team.pk,
                    supplier_id=supplier.pk,
                    document_id=doc.id,
                    item_id=item.id,
                )
                # check for custom fields
                if parsed_item.custom_fields:
                    item_custom_field, created = (
                        ItemCustomField.objects.update_or_create(
                            item=item,
                            defaults={"custom_fields": parsed_item.custom_fields},
                        )
                    )
                    logger.info(
                        "Created "
                        if created
                        else "Updated " + "item custom fields for supplier",
                        team_id=team.pk,
                        supplier_id=supplier.pk,
                        document_id=doc.id,
                        item_id=item.id,
                        item_custom_field_id=item_custom_field.pk,
                    )
            except Exception as e:
                logger.error(
                    f"Error creating item: {e}",
                    team_id=team.pk,
                    supplier_id=supplier.pk,
                    document_id=doc.id,
                    item_id=item.id,
                )
                results["errors"].append(item.item_number)
        logger.info(
            "Finished creating items",
            new_items_count=len(results["new_items"]),
            existing_items_count=len(results["existing_items"]),
            errors_count=len(results["errors"]),
            item_numbers={
                "new_items": results["new_items"],
                "existing_items": results["existing_items"],
                "errors": results["errors"],
            },
        )
        return Response(
            {"message": "Successfully created items from price list."},
            status=status.HTTP_200_OK,
        )

    def destroy(self, request, *args, **kwargs):
        """
        Delete a document with support for both soft delete (archive) and hard delete.

        By default, performs soft delete (archiving). Use ?force=true for hard delete.
        Implementation matches patterns used in suppliers and items models.

        Args:
            request: HTTP request object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments, expects 'uuid'

        Returns:
            Response: 200 for archive operations, 204 for hard delete
        """
        document = get_object_or_404(
            Document, uuid=kwargs["uuid"], team=self.request.team
        )

        # Check for force parameter (case-insensitive)
        force = request.query_params.get("force", "").lower() == "true"

        if force:
            # Hard delete: permanently remove document and all related data
            document.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

        # Soft delete: archive the document
        if not document.archived_at:
            document.archived_at = timezone.now()
            document.save(update_fields=["archived_at"])
            return Response(
                {"message": f"Document {document.uuid} archived successfully"},
                status=status.HTTP_200_OK,
            )

        # Document was already archived
        return Response(
            {
                "message": f"Document {document.uuid} was previously archived at {document.archived_at}"
            },
            status=status.HTTP_200_OK,
        )


class DocumentLinkViewset(APIView, viewsets.ModelViewSet):
    serializer_class = DocumentLinkSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    ordering = ["-created_at"]

    def get_queryset(self):
        uuid = self.kwargs.get("uuid")
        return DocumentLink.objects.filter(
            document__uuid=uuid, document__team=self.request.team
        )

    def perform_create(self, serializer):
        try:
            # get the uuid from the url
            uuid = self.kwargs.get("uuid")
            serializer.is_valid(raise_exception=True)

            # check if the document exists for the team making the request
            document = Document.objects.filter(uuid=uuid).get()
            if document:
                serializer.validated_data["document_id"] = document.id
            else:
                raise ValidationError(
                    f"Cannot create document link for a document that does not exists [uuid = {uuid}; team: {self.request.team.pk}]"
                )

            # get the parent model in order to validate the data
            parent_object_model = PARENT_OBJECT_TYPE_TO_MODEL.get(
                serializer.validated_data.get("parent_object_type")
            )
            serializer.validated_data["parent_object_type"] = (
                get_content_type_for_model(parent_object_model)
            )

            # check if the parent model actually exists
            parent_object_id = serializer.validated_data.get("parent_object_id")
            if not parent_object_model.objects.filter(
                id=parent_object_id, team=self.request.team
            ).exists():
                raise ValidationError(
                    f"The parent object (id = {parent_object_id}) does not exist (team: {self.request.team.pk})!"
                )
        except ValueError as e:
            raise e
        try:
            link = serializer.save()
        except IntegrityError as e:
            raise e
        serializer = self.serializer_class(instance=link)
        return Response(serializer.data)

    # This method catches the perform_create errors and handles them
    # perform_create does not return anything so returning an error response
    # these would do nothing
    def create(self, validated_data, uuid=None):
        try:
            return super().create(validated_data)
        except ValidationError as exc:
            return self.single_error_response(
                detail=exc.args[0],
                attr="document",
                status=status.HTTP_400_BAD_REQUEST,
            )
        except IntegrityError as _:
            return self.single_error_response(
                detail="DocumentLink already exists for the user-team pair.",
                attr="document",
                status=status.HTTP_409_CONFLICT,
            )
        except Exception as exc:
            raise exc

    def destroy(self, request, *args, **kwargs):
        try:
            # Get the document UUID from the URL
            uuid = kwargs.get("uuid")

            # Get the parent_object_type and parent_object_id from request data
            parent_object_type = request.data.get("parent_object_type")
            parent_object_id = request.data.get("parent_object_id")

            if not parent_object_type or not parent_object_id:
                return self.single_error_response(
                    detail="parent_object_type and parent_object_id are required.",
                    attr="root",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the parent model
            parent_model = PARENT_OBJECT_TYPE_TO_MODEL.get(parent_object_type.lower())
            if not parent_model:
                return self.single_error_response(
                    detail=f"Invalid parent_object_type: {parent_object_type}",
                    attr="parent_object_type",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the content type for the parent model
            content_type = get_content_type_for_model(parent_model)

            # Validate that the document exists for the team
            try:
                document = Document.objects.get(uuid=uuid, team=request.team)
            except Document.DoesNotExist:
                return self.single_error_response(
                    detail=f"Cannot delete document link for a document that does not exist [uuid = {uuid}; team: {request.team.pk}]",
                    attr="document",
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Validate that the parent object exists
            if not parent_model.objects.filter(
                id=parent_object_id, team=request.team
            ).exists():
                return self.single_error_response(
                    detail=f"The parent object (id = {parent_object_id}) does not exist (team: {request.team.pk})!",
                    attr="parent_object",
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Find and delete the DocumentLink instance
            document_link = DocumentLink.objects.get(
                document=document,
                parent_object_type=content_type,
                parent_object_id=parent_object_id,
            )
            document_link.delete()

            return Response(status=status.HTTP_200_OK)

        except DocumentLink.DoesNotExist:
            return self.single_error_response(
                detail="DocumentLink not found.",
                attr="documentLink",
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValidationError as exc:
            return self.error_response(
                [
                    {
                        "status": status.HTTP_400_BAD_REQUEST,
                        "attr": "documentLink",
                        "detail": exc.args[0],
                    }
                ],
                status.HTTP_400_BAD_REQUEST,
            )
        except Exception as exc:
            logger.exception(
                "Unexpected error during document link deletion", exc_info=exc
            )
            return self.single_error_response(
                detail="An unexpected error occurred.",
                attr="root",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DocumentCommentViewSet(BaseCommentViewset):
    serializer_class = DocumentCommentSerializer

    def get_parent(self):
        document_uuid = self.kwargs.get("uuid")
        document = Document.objects.filter(
            uuid=document_uuid, team=self.request.team
        ).first()
        if not document:
            raise NotFound("Document not found, or does not belong to this team")
        return document

    def get_parent_model_field(self):
        return "document"
