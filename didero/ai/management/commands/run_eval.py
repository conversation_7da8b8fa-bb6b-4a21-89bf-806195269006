"""
Django management command to run reasoning engine evaluation.

This provides a simple command-line interface to run evaluations:
    python manage.py run_eval
    python manage.py run_eval --report
    python manage.py run_eval --concurrency 5
"""

import asyncio
from typing import Any

from django.core.management.base import BaseCommand

from didero.ai.reasoning_engine.evals.run_evals import (
    generate_evaluation_report,
    run_full_evaluation,
)


class Command(BaseCommand):
    """
    Run reasoning engine evaluation from the command line.

    This command:
    1. Runs the evaluation suite
    2. Saves results to database
    3. Optionally generates a markdown report
    4. Shows results in console
    """

    help = "Run reasoning engine evaluation and save results to database"

    def add_arguments(self, parser: Any) -> None:
        """Define command-line arguments."""
        parser.add_argument(
            "--dataset",
            default="hardcoded_basic",
            help="Dataset to use for evaluation (default: hardcoded_basic)",
        )
        parser.add_argument(
            "--report",
            action="store_true",
            help="Generate and display detailed markdown report",
        )
        parser.add_argument(
            "--concurrency",
            type=int,
            default=1,
            help="Max concurrent test executions (default: 2)",
        )
        parser.add_argument(
            "--quiet",
            action="store_true",
            help="Minimal output - just show final score",
        )

    def handle(self, *args: Any, **options: Any) -> None:
        """
        Main command handler.

        This runs the evaluation and displays results based on the options provided.
        """
        dataset = options["dataset"]
        concurrency = options["concurrency"]
        quiet = options["quiet"]

        if not quiet:
            self.stdout.write(
                self.style.NOTICE(
                    f"\n🚀 Starting evaluation with dataset '{dataset}' "
                    f"(max concurrency: {concurrency})\n"
                )
            )

        # Run evaluation using asyncio
        try:
            result = asyncio.run(
                run_full_evaluation(dataset_name=dataset, max_concurrency=concurrency)
            )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"\n❌ Evaluation failed: {str(e)}\n"))
            return

        # Extract results
        run_id = result["run_id"]
        total_time = result["total_time_seconds"]
        report = result["report"]

        # Get case results from the correct attribute (pydantic-evals uses 'cases')
        case_results = getattr(report, "cases", getattr(report, "case_results", []))

        # Calculate score - check both possible attribute names for scores
        passed = 0
        for r in case_results:
            score_value = 0

            # pydantic-evals flattens evaluator results into individual score keys
            if hasattr(r, "scores") and isinstance(r.scores, dict):
                # The scores dict has keys for each score returned by evaluators
                # Each value is an EvaluationResult object with a .value attribute
                eval_result = r.scores.get("overall_score")
                if eval_result and hasattr(eval_result, "value"):
                    # The value is the actual score (float)
                    score_value = (
                        float(eval_result.value)
                        if eval_result.value is not None
                        else 0.0
                    )

            if score_value >= 1.0:
                passed += 1

        total = len(case_results)
        score = (passed / total * 100) if total > 0 else 0

        # Display results
        if quiet:
            # Minimal output
            self.stdout.write(f"{score:.1f}%")
        else:
            # Full output
            self.stdout.write(
                self.style.SUCCESS(f"\n✅ Evaluation complete in {total_time:.1f}s\n")
            )

            # Show summary
            self.stdout.write("\n📊 Results Summary:")
            self.stdout.write(f"   Run ID: {run_id}")
            self.stdout.write(f"   Score: {score:.1f}% ({passed}/{total} passed)")
            self.stdout.write(f"   Time: {total_time:.1f} seconds")

            # Show individual test results
            self.stdout.write("\n📋 Test Results:")
            # Use the correct attribute name for pydantic-evals
            test_cases = getattr(report, "cases", getattr(report, "case_results", []))
            for case_result in test_cases:
                # Get score from flattened evaluator results in case_result.scores dict
                workflow_score = 0.0
                if hasattr(case_result, "scores") and isinstance(
                    case_result.scores, dict
                ):
                    # pydantic-evals flattens results - look for overall_score directly
                    eval_result = case_result.scores.get("overall_score")
                    if eval_result and hasattr(eval_result, "value"):
                        # The value is the actual score (float)
                        workflow_score = (
                            float(eval_result.value)
                            if eval_result.value is not None
                            else 0.0
                        )

                status = "✅" if workflow_score >= 1.0 else "❌"

                # Get additional actions from flattened evaluator results
                additional = {}
                if hasattr(case_result, "scores") and isinstance(
                    case_result.scores, dict
                ):
                    # Build additional actions from individual score keys
                    # The actual lists are stored as string representations
                    extra_workflows_result = case_result.scores.get("extra_workflows")
                    database_updates_result = case_result.scores.get("database_updates")
                    human_tasks_result = case_result.scores.get("human_tasks")

                    additional = {
                        "extra_workflows": str(extra_workflows_result.value)
                        if extra_workflows_result
                        and hasattr(extra_workflows_result, "value")
                        else "",
                        "database_updates": str(database_updates_result.value)
                        if database_updates_result
                        and hasattr(database_updates_result, "value")
                        else "",
                        "human_tasks": str(human_tasks_result.value)
                        if human_tasks_result and hasattr(human_tasks_result, "value")
                        else "",
                    }

                # Count additional actions
                extra_count = 0
                if additional:
                    extra_count = (
                        len(additional.get("extra_workflows", []))
                        + len(additional.get("database_updates", []))
                        + len(additional.get("human_tasks", []))
                    )

                extra_str = f" (+{extra_count} actions)" if extra_count > 0 else ""
                self.stdout.write(f"   {status} {case_result.name}{extra_str}")

            # Show additional actions summary
            total_additional = 0
            for case_result in test_cases:
                if hasattr(case_result, "scores") and isinstance(
                    case_result.scores, dict
                ):
                    # Get total_additional_actions from flattened scores
                    eval_result = case_result.scores.get("total_additional_actions")
                    if eval_result and hasattr(eval_result, "value"):
                        actions_count = eval_result.value
                        total_additional += (
                            int(actions_count) if actions_count is not None else 0
                        )

            if total_additional > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f"\n💡 Discovered {total_additional} additional actions "
                        f"beyond primary workflows!"
                    )
                )

        # Generate detailed report if requested
        if options["report"]:
            self.stdout.write("\n" + "=" * 60)
            markdown_report = generate_evaluation_report(run_id)
            self.stdout.write(markdown_report)
            self.stdout.write("=" * 60 + "\n")

        # Always show where to view results
        if not quiet:
            self.stdout.write(
                self.style.NOTICE(
                    f"\n👀 View detailed results in Django admin: "
                    f"/admin/ai/evaluationrun/{run_id}/\n"
                )
            )
