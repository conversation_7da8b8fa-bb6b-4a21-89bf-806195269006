"""
Main evaluation runner for the Reasoning Engine.

This module orchestrates the evaluation process:
1. Runs test cases through the reasoning engine
2. Calls evaluators to score results
3. Saves everything to the database
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict, List

import structlog
from django.utils import timezone

logger = structlog.get_logger(__name__)


async def run_full_evaluation(
    dataset_name: str = "hardcoded_basic", max_concurrency: int = 2
) -> Dict[str, Any]:
    """
    Run complete evaluation suite using pydantic-evals.

    This is the main entry point for running evaluations. It:
    1. Gets the test dataset
    2. Creates a wrapper function for the reasoning engine
    3. Lets pydantic-evals run all tests with evaluators
    4. Saves results to database
    5. Returns summary

    Args:
        dataset_name: Which dataset to use (currently only "hardcoded_basic")
        max_concurrency: How many tests to run in parallel

    Returns:
        Dict with run_id, dataset name, report, and timing
    """
    from didero.ai.reasoning_engine.orchestrator import email_reasoning_engine

    from .test_cases import create_mock_communication, get_evaluation_dataset

    start_time = datetime.now()

    # Get dataset with evaluators already attached
    dataset = get_evaluation_dataset()

    # Create wrapper function that pydantic-evals will call for each test case
    async def evaluate_reasoning_engine(inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Wrapper that creates mock Communication and runs reasoning engine.

        This function is called by pydantic-evals for each test case.
        It creates a mock email and runs it through the reasoning engine.
        """
        # Create mock Communication to avoid database operations
        mock_comm = create_mock_communication(
            email_text=inputs["email"],
            team_id=inputs.get("team_id", 1),
            subject=f"Test: {inputs.get('description', 'test')}",
            test_name=inputs.get("description", "test"),
        )

        try:
            # Run reasoning engine on the mock email
            response = await email_reasoning_engine(
                mock_comm, disable_side_effects=True
            )

            # Return the reasoning result as a dict
            if response and response.reasoning:
                return response.reasoning.model_dump()
            else:
                # Failed to process - return empty result
                return {
                    "workflows_triggered": [],
                    "database_updates": [],
                    "unhandled_cases": [],
                    "summary": "Failed to process",
                    "success": False,
                }
        except Exception as e:
            return {
                "workflows_triggered": [],
                "database_updates": [],
                "unhandled_cases": [],
                "summary": f"Error: {str(e)}",
                "success": False,
                "error": str(e),
            }

    # Let pydantic-evals handle the evaluation loop

    # Run all test cases through the reasoning engine
    report = await dataset.evaluate(
        evaluate_reasoning_engine, max_concurrency=max_concurrency
    )

    # Get the case results from pydantic-evals report
    case_results = report.cases

    # Save results to database
    run = await save_evaluation_to_database(report, dataset_name, case_results)

    # Print report to console
    report.print(
        include_input=True,
        include_output=False,  # Output might be too verbose
        include_durations=True,
    )

    total_time = (datetime.now() - start_time).total_seconds()

    return {
        "run_id": str(run.uuid),
        "dataset": dataset_name,
        "report": report,
        "total_time_seconds": total_time,
    }


async def save_evaluation_to_database(
    report: Any, dataset_name: str, case_results: Any = None
) -> Any:
    """
    Save pydantic-evals report to our Django models.

    This takes the report from pydantic-evals and stores it in our
    database for historical tracking and analysis.

    Args:
        report: The EvaluationReport from pydantic-evals
        dataset_name: Name of the dataset used

    Returns:
        The created EvaluationRun instance
    """
    from asgiref.sync import sync_to_async

    from didero.ai.models import EvaluationResult, EvaluationRun

    # Use provided case_results or get from report
    if case_results is None:
        case_results = report.cases if hasattr(report, "cases") else []

    def _get_entry_value(scores: Dict[str, Any], key: str) -> Any:
        entry = scores.get(key)
        if entry is None:
            return None
        return getattr(entry, "value", entry)

    # Count how many tests passed
    passed_count = 0
    for case_result in case_results:
        try:
            score = 0.0
            if hasattr(case_result, "scores") and isinstance(case_result.scores, dict):
                raw_value = _get_entry_value(case_result.scores, "overall_score")
                if raw_value is not None:
                    try:
                        score = float(raw_value)
                    except (TypeError, ValueError):
                        score = 0.0

            if score >= 1.0:
                passed_count += 1
        except (TypeError, AttributeError, ValueError) as e:
            logger.warning(
                "Could not extract score from case result",
                case_name=getattr(case_result, "name", "unknown"),
                error=str(e),
            )

    # Create the run record (parent record for this batch)
    run = await sync_to_async(EvaluationRun.objects.create)(
        dataset_name=dataset_name,
        system_tested="reasoning",
        total_cases=len(case_results) if case_results else 0,
        passed_cases=passed_count,
        overall_score=passed_count / len(case_results) if case_results else 0,
        configuration={"max_concurrency": 2, "evaluators": ["ActionMatchEvaluator"]},
        completed_at=timezone.now(),
    )

    # Save individual test results
    for case_result in case_results or []:
        overall_score = 0.0
        action_metrics: Dict[str, Any] = {}

        try:
            scores_dict = (
                case_result.scores
                if hasattr(case_result, "scores")
                and isinstance(case_result.scores, dict)
                else {}
            )

            if scores_dict:
                def _float(key: str, default: float = 0.0) -> float:
                    value = _get_entry_value(scores_dict, key)
                    if value is None:
                        return default
                    try:
                        return float(value)
                    except (TypeError, ValueError):
                        return default

                def _int(key: str, default: int = 0) -> int:
                    value = _get_entry_value(scores_dict, key)
                    if value is None:
                        return default
                    try:
                        return int(float(value))
                    except (TypeError, ValueError):
                        return default

                def _json_list(key: str) -> List[Any]:
                    raw = _get_entry_value(scores_dict, key)
                    if raw is None:
                        return []
                    if isinstance(raw, list):
                        return raw
                    if isinstance(raw, str):
                        try:
                            return json.loads(raw)
                        except (TypeError, ValueError, json.JSONDecodeError):
                            return []
                    try:
                        return list(raw)
                    except TypeError:
                        return []

                def _json_object(key: str) -> Dict[str, Any]:
                    raw = _get_entry_value(scores_dict, key)
                    if raw is None:
                        return {}
                    if isinstance(raw, dict):
                        return raw
                    if isinstance(raw, str):
                        try:
                            return json.loads(raw)
                        except (TypeError, ValueError, json.JSONDecodeError):
                            return {}
                    try:
                        return dict(raw)
                    except TypeError:
                        return {}

                overall_score = _float("overall_score", 0.0)
                overall_expected = _int("overall_expected", 0)
                overall_matched = _int("overall_matched", 0)
                overall_missing = _int("overall_missing", 0)
                overall_unexpected = _int("overall_unexpected", 0)
                unexpected_flag = _float("unexpected_actions_present", 0.0)

                workflows_expected = _int("workflows_expected", 0)
                workflows_matched = _int("workflows_matched", 0)
                workflows_missing = _int("workflows_missing", 0)
                workflows_unexpected = _int("workflows_unexpected", 0)
                workflows_score = _float("workflows_score", 0.0)

                db_expected = _int("database_updates_expected", 0)
                db_matched = _int("database_updates_matched", 0)
                db_missing = _int("database_updates_missing", 0)
                db_unexpected = _int("database_updates_unexpected", 0)
                db_score = _float("database_updates_score", 0.0)

                human_expected = _int("human_tasks_expected", 0)
                human_matched = _int("human_tasks_matched", 0)
                human_missing = _int("human_tasks_missing", 0)
                human_unexpected = _int("human_tasks_unexpected", 0)
                human_score = _float("human_tasks_score", 0.0)

                expected_actions_data = _json_object("expected_actions_serialized")
                actual_workflows = _json_list("actual_workflows")
                actual_db_updates = _json_list("actual_database_updates")
                actual_human_tasks = _json_list("actual_human_tasks")

                action_metrics = {
                    "overall": {
                        "expected": overall_expected,
                        "matched": overall_matched,
                        "missing": overall_missing,
                        "unexpected": overall_unexpected,
                        "score": overall_score,
                        "unexpected_flag": unexpected_flag,
                    },
                    "workflows": {
                        "expected": workflows_expected,
                        "matched": workflows_matched,
                        "missing": workflows_missing,
                        "unexpected": workflows_unexpected,
                        "score": workflows_score,
                        "missing_items": _json_list("workflows_missing_items"),
                        "unexpected_items": _json_list("workflows_unexpected_items"),
                    },
                    "database_updates": {
                        "expected": db_expected,
                        "matched": db_matched,
                        "missing": db_missing,
                        "unexpected": db_unexpected,
                        "score": db_score,
                        "missing_items": _json_list("database_updates_missing_items"),
                        "unexpected_items": _json_list(
                            "database_updates_unexpected_items"
                        ),
                    },
                    "human_tasks": {
                        "expected": human_expected,
                        "matched": human_matched,
                        "missing": human_missing,
                        "unexpected": human_unexpected,
                        "score": human_score,
                        "missing_items": _json_list("human_tasks_missing_items"),
                        "unexpected_items": _json_list(
                            "human_tasks_unexpected_items"
                        ),
                    },
                    "actual": {
                        "workflows": actual_workflows,
                        "database_updates": actual_db_updates,
                        "human_tasks": actual_human_tasks,
                    },
                    "expected": expected_actions_data,
                }

        except (TypeError, AttributeError, ValueError) as e:
            logger.warning(
                "Could not extract scores from evaluator results",
                case_name=getattr(case_result, "name", "unknown"),
                error=str(e),
                has_result=hasattr(case_result, "result"),
                result_type=type(case_result.result).__name__
                if hasattr(case_result, "result")
                else "no result",
                result_length=len(case_result.result)
                if hasattr(case_result, "result") and case_result.result
                else 0,
            )
            overall_score = 0.0
            action_metrics = {}

        # Store everything as simple JSON
        await sync_to_async(EvaluationResult.objects.create)(
            run=run,
            test_name=case_result.name,
            email_text=case_result.inputs.get("email", ""),
            expected_category=case_result.expected_output.get("category")
            if case_result.expected_output
            else None,
            actual_output=case_result.output
            if isinstance(case_result.output, dict)
            else {},
            additional_actions_found=action_metrics,
            passed=overall_score >= 1.0,
            score=overall_score,
            execution_time_ms=int(case_result.duration.total_seconds() * 1000)
            if hasattr(case_result, "duration") and case_result.duration
            else None,
            error_message=str(case_result.error)
            if hasattr(case_result, "error") and case_result.error
            else None,
        )

    return run


def generate_evaluation_report(run_id: str) -> str:
    """
    Generate markdown report from evaluation results.

    This creates a human-readable report showing:
    - Overall score
    - Individual test results
    - Additional actions discovered

    Args:
        run_id: UUID of the evaluation run

    Returns:
        Markdown formatted report string
    """
    from didero.ai.models import EvaluationRun

    run = EvaluationRun.objects.get(uuid=run_id)
    results = run.results.all()  # type: ignore[attr-defined]

    report = f"""# Evaluation Report

**Run ID**: {run.uuid}
**Dataset**: {run.dataset_name}
**Date**: {run.created_at.strftime('%Y-%m-%d %H:%M:%S')}
**Overall Score**: {run.overall_score:.1%} ({run.passed_cases}/{run.total_cases})

## Results by Test Case

| Test Case | Passed | Score | Action Summary |
|-----------|--------|-------|----------------|
"""

    for result in results:
        additional = result.additional_actions_found or {}
        action_summary = []

        workflows_metrics = additional.get("workflows", {})
        if workflows_metrics:
            action_summary.append(
                f"WF {workflows_metrics.get('matched', 0)}/"
                f"{workflows_metrics.get('expected', 0)}"
            )

        db_metrics = additional.get("database_updates", {})
        if db_metrics:
            action_summary.append(
                f"DB {db_metrics.get('matched', 0)}/"
                f"{db_metrics.get('expected', 0)}"
            )

        human_metrics = additional.get("human_tasks", {})
        if human_metrics:
            action_summary.append(
                f"Tasks {human_metrics.get('matched', 0)}/"
                f"{human_metrics.get('expected', 0)}"
            )

        unexpected = additional.get("overall", {}).get("unexpected", 0)
        missing = additional.get("overall", {}).get("missing", 0)
        if unexpected:
            action_summary.append(f"{unexpected} unexpected")
        if missing:
            action_summary.append(f"{missing} missing")

        action_str = ", ".join(action_summary) if action_summary else "--"
        status = "✅" if result.passed else "❌"

        report += (
            f"| {result.test_name} | {status} | {result.score:.1f} | {action_str} |\n"
        )

    return report


if __name__ == "__main__":
    # Simple CLI interface for testing
    import argparse

    parser = argparse.ArgumentParser(description="Run reasoning engine evaluation")
    parser.add_argument("--dataset", default="hardcoded_basic", help="Dataset to use")

    args = parser.parse_args()

    # Run evaluation
    result = asyncio.run(run_full_evaluation(dataset_name=args.dataset))
    print(f"\nEvaluation complete: Run ID {result['run_id']}")
