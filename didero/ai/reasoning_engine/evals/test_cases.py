"""
Test cases for evaluation system.

This module contains:
1. Hardcoded test cases covering main email categories
2. Mock communication helper to avoid database operations
3. Dataset builder for pydantic-evals
"""

from typing import List
from unittest.mock import Mock

from pydantic_evals import Case, Dataset  # type: ignore[import-untyped]

from didero.emails.schemas import EmailCategory

from .evaluators import ActionMatchEvaluator
from .schemas import EvaluationTestCase, ExpectedActions


def create_mock_communication(
    email_text: str, team_id: int, subject: str = "Test Email", test_name: str = "test"
) -> Mock:
    """
    Create a mock Communication object for testing.

    This avoids:
    - Database operations (no actual records created)
    - Side effects (no workflows triggered, no emails sent)
    - Dependencies (no need for real Team, Supplier, etc.)

    The mock only provides the attributes that the reasoning engine actually uses.
    """
    from django.utils import timezone

    mock_comm = Mock()

    # Basic attributes the reasoning engine expects
    mock_comm.pk = f"test_{test_name}"
    mock_comm.subject = subject
    mock_comm.body_text = email_text
    mock_comm.direction = "INBOUND"
    mock_comm.email_message_id = (
        f"test_{test_name}_{timezone.now().timestamp()}@example.com"
    )

    # Mock the team relationship
    mock_team = Mock()
    mock_team.id = team_id
    mock_comm.team = mock_team

    # Mock the display method that reasoning engine calls
    mock_comm.as_display_string = Mock(return_value=email_text)

    # Mock email thread (usually None for test emails)
    mock_comm.email_thread = None

    return mock_comm


# Our 5 hardcoded test cases covering main scenarios
CURATED_TEST_CASES: List[EvaluationTestCase] = [
    EvaluationTestCase(
        name="invoice_basic",
        email="Please find attached invoice #12345 for $1,000 for the widgets ordered last month. Payment is due within 30 days.",
        category=EmailCategory.INVOICE,
        description="Simple invoice that should trigger invoice_processing workflow",
        expected_actions=ExpectedActions(
            workflows=["invoice_processing"],
        ),
    ),
    EvaluationTestCase(
        name="po_creation",
        email="PO-2024-001 for 100 units of SKU-789 at $50 each. Ship to warehouse A by end of month.",
        category=EmailCategory.PURCHASE_ORDER,
        description="Clear purchase order that should trigger po_creation workflow",
        expected_actions=ExpectedActions(
            workflows=["po_creation"],
        ),
    ),
    EvaluationTestCase(
        name="shipment_notice",
        email="Your order #PO-123 has shipped via FedEx tracking 123456789. Expected delivery is Thursday.",
        category=EmailCategory.ORDER_SHIPPED,
        description="Shipment notification that should trigger shipment workflow",
        expected_actions=ExpectedActions(
            workflows=["shipment_processing"],
        ),
    ),
    EvaluationTestCase(
        name="complex_multi_action",
        email="Invoice #999 attached for PO-456. Also, please update delivery date to next Friday. The pricing seems off - can you confirm?",
        category=EmailCategory.INVOICE,
        description="Invoice with additional database update request and human review needed",
        metadata={"expects_additional_actions": True},
        expected_actions=ExpectedActions(
            workflows=["invoice_processing"],
            database_updates=["PurchaseOrder.delivery_date"],
            human_tasks=["Review pricing discrepancy for PO-456"],
        ),
    ),
    EvaluationTestCase(
        name="unhandled_edge",
        email="Can you expedite my order? It's urgent for our client meeting tomorrow morning.",
        category=EmailCategory.OTHER,
        description="Edge case requiring human intervention - no standard workflow",
        metadata={"expects_human_task": True},
        expected_actions=ExpectedActions(
            human_tasks=["Clarify expedited order request"],
        ),
    ),
]


def get_evaluation_dataset() -> Dataset:
    """
    Build dataset with evaluators attached for pydantic-evals.

    This converts our typed EvaluationTestCase objects into the format
    that pydantic-evals expects (Case objects with inputs/expected_output).

    The evaluator is attached to the dataset, so it will automatically
    be called for each test case.
    """
    cases = []

    for test_case in CURATED_TEST_CASES:
        cases.append(
            Case(
                name=test_case.name,
                inputs={
                    "email": test_case.email,
                    "team_id": test_case.team_id,
                    "description": test_case.description,
                },
                expected_output={
                    "category": test_case.category.value,  # Convert enum to string
                    "metadata": test_case.metadata,
                    "actions": test_case.expected_actions.model_dump(),
                },
                metadata=test_case.metadata,
            )
        )

    # Create dataset with evaluator attached
    dataset = Dataset(
        cases=cases,
        # name="hardcoded_basic",  # Removed as Dataset doesn't have name parameter
        evaluators=[ActionMatchEvaluator()],
    )

    return dataset
