"""Unit tests for the ActionMatchEvaluator."""

from types import SimpleNamespace

import pytest

from didero.ai.reasoning_engine.evals.evaluators import ActionMatchEvaluator
from didero.ai.reasoning_engine.evals.schemas import ExpectedActions


def _make_ctx(output: dict, expected_actions: ExpectedActions) -> SimpleNamespace:
    return SimpleNamespace(
        output=output,
        expected_output={"actions": expected_actions.model_dump()},
    )


def test_action_match_perfect():
    evaluator = ActionMatchEvaluator()
    output = {
        "workflows_triggered": [{"workflow_type": "invoice_processing"}],
        "database_updates": [],
        "unhandled_cases": [],
    }
    ctx = _make_ctx(output, ExpectedActions(workflows=["invoice_processing"]))

    result = evaluator.evaluate(ctx)

    assert pytest.approx(result["overall_score"]) == 1.0
    assert result["overall_missing"] == 0
    assert result["overall_unexpected"] == 0


def test_action_match_partial_missing():
    evaluator = ActionMatchEvaluator()
    output = {
        "workflows_triggered": [{"workflow_type": "invoice_processing"}],
        "database_updates": [],
        "unhandled_cases": [],
    }
    expected = ExpectedActions(workflows=["invoice_processing", "po_creation"])
    ctx = _make_ctx(output, expected)

    result = evaluator.evaluate(ctx)

    assert pytest.approx(result["overall_score"]) == 0.5
    assert result["overall_missing"] == 1
    assert result["workflows_missing"] == 1


def test_action_match_unexpected_without_permission():
    evaluator = ActionMatchEvaluator()
    output = {
        "workflows_triggered": [{"workflow_type": "invoice_processing"}],
        "database_updates": [],
        "unhandled_cases": [],
    }
    ctx = _make_ctx(output, ExpectedActions())

    result = evaluator.evaluate(ctx)

    assert result["overall_score"] == 0.0
    assert result["overall_unexpected"] == 1


def test_action_match_unexpected_allowed():
    evaluator = ActionMatchEvaluator()
    output = {
        "workflows_triggered": [{"workflow_type": "invoice_processing"}],
        "database_updates": [],
        "unhandled_cases": [],
    }
    ctx = _make_ctx(
        output,
        ExpectedActions(allow_unexpected_actions=True),
    )

    result = evaluator.evaluate(ctx)

    assert result["overall_score"] == 1.0
    assert result["overall_unexpected"] == 1
