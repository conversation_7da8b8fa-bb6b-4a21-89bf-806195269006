"""
Simple schemas for the evaluation system.

We keep things straightforward:
- Test cases are simple dicts
- Results are stored as JSON
- No complex type preservation needed
"""

from typing import Any, Dict, List

from pydantic import BaseModel, Field

from didero.emails.schemas import EmailCategory


class ExpectedActions(BaseModel):
    """Expected actions the reasoning agent should deliver for a test case."""

    workflows: List[str] = Field(
        default_factory=list,
        description="Workflow identifiers the agent should trigger",
    )
    database_updates: List[str] = Field(
        default_factory=list,
        description="Database updates expected, formatted as 'Model.field'",
    )
    human_tasks: List[str] = Field(
        default_factory=list,
        description="Human task titles or descriptors the agent should create",
    )
    allow_unexpected_actions: bool = Field(
        default=False,
        description="If true, unexpected actions do not fail the test",
    )

    def total_expected(self) -> int:
        """Return the total number of expected actions across all types."""
        return (
            len(self.workflows)
            + len(self.database_updates)
            + len(self.human_tasks)
        )


class EvaluationTestCase(BaseModel):
    """
    Simple test case definition.

    Each test case represents one email we want to test and the
    concrete actions we expect the reasoning agent to take.
    """

    name: str = Field(description="Test identifier like 'invoice_basic'")
    email: str = Field(description="Email content to test")
    category: EmailCategory = Field(
        description="Narrative email category for reporting and analytics"
    )
    expected_actions: ExpectedActions = Field(
        description="Actions the reasoning engine should perform for this test"
    )
    team_id: int = Field(default=1, description="Team ID for test context")
    description: str = Field(description="What this test validates")
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Optional metadata like 'expects_additional_actions'",
    )


class AdditionalActions(BaseModel):
    """
    Simple container for additional actions discovered.

    This is the GOLD - what extra value did the reasoning engine find
    beyond just categorizing the email correctly?

    We store everything as simple strings for easy reading and querying.
    """

    extra_workflows: List[str] = Field(
        default_factory=list, description="Workflow types beyond the primary one"
    )
    database_updates: List[str] = Field(
        default_factory=list,
        description="Simple field references like 'PO.delivery_date'",
    )
    human_tasks: List[str] = Field(
        default_factory=list,
        description="Task descriptions that need human intervention",
    )

    def get_summary(self) -> Dict[str, int]:
        """Get counts for metrics."""
        return {
            "extra_workflows": len(self.extra_workflows),
            "database_updates": len(self.database_updates),
            "human_tasks": len(self.human_tasks),
            "total": len(self.extra_workflows)
            + len(self.database_updates)
            + len(self.human_tasks),
        }
