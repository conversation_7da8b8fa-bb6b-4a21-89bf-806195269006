"""Evaluators for the reasoning engine evaluation system."""

from __future__ import annotations

import json
from typing import Any, Dict, Iterable, List, Sequence

from pydantic_evals.evaluators import (  # type: ignore[import-untyped]
    Evaluator,
    EvaluatorContext,
)

from .schemas import ExpectedActions


class ActionMatchEvaluator(Evaluator):
    """Scores how well the reasoning agent's actions match expectations."""

    def evaluate(self, ctx: EvaluatorContext) -> Dict[str, Any]:
        expected_actions_raw = {}
        if ctx.expected_output:
            expected_actions_raw = ctx.expected_output.get("actions", {}) or {}

        expected_actions = ExpectedActions.model_validate(expected_actions_raw)
        output = ctx.output

        actual_workflows = self._extract_triggered_workflows(output)
        actual_db_updates = self._extract_database_updates(output)
        actual_human_tasks = self._extract_human_tasks(output)

        workflow_metrics = self._score_dimension(
            expected_actions.workflows,
            actual_workflows,
            expected_actions.allow_unexpected_actions,
        )
        db_update_metrics = self._score_dimension(
            expected_actions.database_updates,
            actual_db_updates,
            expected_actions.allow_unexpected_actions,
        )
        human_task_metrics = self._score_dimension(
            expected_actions.human_tasks,
            actual_human_tasks,
            expected_actions.allow_unexpected_actions,
        )

        total_expected = (
            workflow_metrics["expected"]
            + db_update_metrics["expected"]
            + human_task_metrics["expected"]
        )
        total_matched = (
            workflow_metrics["matched"]
            + db_update_metrics["matched"]
            + human_task_metrics["matched"]
        )
        total_missing = (
            workflow_metrics["missing"]
            + db_update_metrics["missing"]
            + human_task_metrics["missing"]
        )
        total_unexpected = (
            workflow_metrics["unexpected"]
            + db_update_metrics["unexpected"]
            + human_task_metrics["unexpected"]
        )

        overall_score = (
            float(total_matched) / float(total_expected)
            if total_expected > 0
            else (
                1.0
                if expected_actions.allow_unexpected_actions or total_unexpected == 0
                else 0.0
            )
        )

        if (
            not expected_actions.allow_unexpected_actions
            and total_unexpected > 0
        ):
            # Unexpected actions without permission should fail the run
            overall_score = 0.0

        return {
            "overall_score": overall_score,
            "overall_expected": total_expected,
            "overall_matched": total_matched,
            "overall_missing": total_missing,
            "overall_unexpected": total_unexpected,
            "workflows_expected": workflow_metrics["expected"],
            "workflows_matched": workflow_metrics["matched"],
            "workflows_missing": workflow_metrics["missing"],
            "workflows_unexpected": workflow_metrics["unexpected"],
            "workflows_score": workflow_metrics["score"],
            "workflows_missing_items": json.dumps(
                workflow_metrics["missing_items"]
            ),
            "workflows_unexpected_items": json.dumps(
                workflow_metrics["unexpected_items"]
            ),
            "database_updates_expected": db_update_metrics["expected"],
            "database_updates_matched": db_update_metrics["matched"],
            "database_updates_missing": db_update_metrics["missing"],
            "database_updates_unexpected": db_update_metrics["unexpected"],
            "database_updates_score": db_update_metrics["score"],
            "database_updates_missing_items": json.dumps(
                db_update_metrics["missing_items"]
            ),
            "database_updates_unexpected_items": json.dumps(
                db_update_metrics["unexpected_items"]
            ),
            "human_tasks_expected": human_task_metrics["expected"],
            "human_tasks_matched": human_task_metrics["matched"],
            "human_tasks_missing": human_task_metrics["missing"],
            "human_tasks_unexpected": human_task_metrics["unexpected"],
            "human_tasks_score": human_task_metrics["score"],
            "human_tasks_missing_items": json.dumps(
                human_task_metrics["missing_items"]
            ),
            "human_tasks_unexpected_items": json.dumps(
                human_task_metrics["unexpected_items"]
            ),
            "unexpected_actions_present": 1.0 if total_unexpected > 0 else 0.0,
            "expected_actions_serialized": json.dumps(expected_actions_raw),
            "actual_workflows": json.dumps(sorted(actual_workflows)),
            "actual_database_updates": json.dumps(sorted(actual_db_updates)),
            "actual_human_tasks": json.dumps(sorted(actual_human_tasks)),
        }

    def _score_dimension(
        self,
        expected: Sequence[str],
        actual: Iterable[str],
        allow_unexpected: bool,
    ) -> Dict[str, Any]:
        expected_set = set(expected)
        actual_set = set(actual)

        matched = expected_set & actual_set
        missing = expected_set - actual_set
        unexpected = actual_set - expected_set

        expected_count = len(expected_set)
        matched_count = len(matched)
        missing_count = len(missing)
        unexpected_count = len(unexpected)

        if expected_count == 0:
            if unexpected_count == 0 or allow_unexpected:
                score = 1.0
            else:
                score = 0.0
        else:
            score = matched_count / expected_count

        return {
            "expected": expected_count,
            "matched": matched_count,
            "missing": missing_count,
            "unexpected": unexpected_count,
            "score": score,
            "matched_items": sorted(matched),
            "missing_items": sorted(missing),
            "unexpected_items": sorted(unexpected),
        }

    def _extract_triggered_workflows(self, output: Any) -> List[str]:
        if hasattr(output, "workflows_triggered"):
            return [w.workflow_type for w in output.workflows_triggered]
        if isinstance(output, dict) and "workflows_triggered" in output:
            workflows = output["workflows_triggered"] or []
            result = []
            for workflow in workflows:
                if isinstance(workflow, dict):
                    workflow_type = workflow.get("workflow_type")
                else:
                    workflow_type = getattr(workflow, "workflow_type", None)
                if workflow_type:
                    result.append(workflow_type)
            return result
        return []

    def _extract_database_updates(self, output: Any) -> List[str]:
        updates: List[str] = []
        if hasattr(output, "database_updates"):
            for update in getattr(output, "database_updates", []):
                model = getattr(update, "model", None)
                field = getattr(update, "field", None)
                if model and field:
                    updates.append(f"{model}.{field}")
        elif isinstance(output, dict) and "database_updates" in output:
            for update in output.get("database_updates", []) or []:
                model = update.get("model") if isinstance(update, dict) else None
                field = update.get("field") if isinstance(update, dict) else None
                if model and field:
                    updates.append(f"{model}.{field}")
        return updates

    def _extract_human_tasks(self, output: Any) -> List[str]:
        tasks: List[str] = []
        if hasattr(output, "unhandled_cases"):
            for case in getattr(output, "unhandled_cases", []):
                task_title = None
                if hasattr(case, "task_created"):
                    task_created = getattr(case, "task_created")
                    if isinstance(task_created, dict):
                        task_title = task_created.get("title")
                if not task_title and hasattr(case, "reason_unhandled"):
                    task_title = getattr(case, "reason_unhandled")
                if task_title:
                    tasks.append(task_title)
        elif isinstance(output, dict) and "unhandled_cases" in output:
            for case in output.get("unhandled_cases", []) or []:
                task_title = None
                if isinstance(case, dict):
                    if isinstance(case.get("task_created"), dict):
                        task_title = case["task_created"].get("title")
                    if not task_title:
                        task_title = case.get("reason_unhandled")
                if task_title:
                    tasks.append(task_title)
        return tasks
