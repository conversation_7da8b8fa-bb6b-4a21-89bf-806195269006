"""
Evaluation system for the Reasoning Engine.

This module provides tools to evaluate the reasoning engine's performance,
track additional actions discovered, and compare results over time.
"""

from .evaluators import ActionMatchEvaluator
from .run_evals import (
    generate_evaluation_report,
    run_full_evaluation,
)
from .schemas import AdditionalActions, EvaluationTestCase, ExpectedActions
from .test_cases import (
    CURATED_TEST_CASES,
    create_mock_communication,
    get_evaluation_dataset,
)

__all__ = [
    # Evaluators
    "ActionMatchEvaluator",
    # Schemas
    "EvaluationTestCase",
    "ExpectedActions",
    "AdditionalActions",
    # Test cases and helpers
    "CURATED_TEST_CASES",
    "create_mock_communication",
    "get_evaluation_dataset",
    # Runner functions
    "run_full_evaluation",
    "generate_evaluation_report",
]
