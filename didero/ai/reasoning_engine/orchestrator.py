"""
Main orchestrator for the Reasoning Engine.

Coordinates the execution of Context Builder and Reasoning agents in sequence.
This implements the "Programmatic Agent Hand-off" pattern where the output
of one agent becomes the input to the next.

Reference: https://ai.pydantic.dev/multi-agent-applications/
"""

from pathlib import Path
from typing import Any, Optional

import structlog
from pydantic_ai.exceptions import FallbackExceptionGroup
from pydantic_ai.usage import UsageLimits

from didero.ai.email_categorization.utils import should_process_email
from didero.ai.observability import configure_observability
from didero.ai.utils import AIOperationContext, get_langfuse_prompt
from didero.suppliers.models import Communication
from didero.users.models import User

from .agents.agents import reasoning_agent
from .schemas.dependencies import (
    ContextBuilderDependencies,
    ReasoningDependencies,
)
from .schemas.results import (
    ContextBuilderResult,
    EmailReasoningEngineResponse,
    ReasoningResult,
)

logger = structlog.get_logger(__name__)


async def email_reasoning_engine(
    email: Communication, disable_side_effects: bool = False
) -> EmailReasoningEngineResponse:
    """
    Main entry point for processing an email through the reasoning engine.

    This is the reasoning engine equivalent of ai_categorize_email.
    Takes a Communication object and processes it through the two-agent flow:
    1. Context Builder extracts information and builds context (currently pass-through)
    2. Reasoning Agent makes decisions based on the context

    Args:
        email: Communication object containing the email to process

    Returns:
        EmailReasoningEngineResponse containing:
        - context: ContextBuilderResult object
        - reasoning: ReasoningResult object
        - success: Overall success status
        - email_id: The ID of the processed email
        - team_id: The team ID
    """
    from asgiref.sync import sync_to_async

    # Check if email should be processed (same as categorizer)
    if not disable_side_effects and not should_process_email(email):
        logger.info(
            "Email not eligible for processing (AI disabled or too old)",
            email_id=email.pk,
            team_id=email.team.id,
        )
        return EmailReasoningEngineResponse(
            context=None,
            reasoning=None,
            success=False,
            email_id=email.pk,
            team_id=email.team.id,
        )

    # Get team and related data with sync_to_async
    team_id = await sync_to_async(lambda: email.team.id)()

    # Get the team object if needed
    from didero.users.models import Team

    team = await sync_to_async(Team.objects.get)(pk=team_id)

    # Get email thread ID if exists
    email_thread_id = None
    email_thread = await sync_to_async(lambda: email.email_thread)()
    if email_thread:
        email_thread_id = await sync_to_async(lambda: email_thread.id)()

    # Setup observability context
    with AIOperationContext(
        trace_name="email_reasoning_engine",
        team_id=str(team_id),
        metadata={
            "email_id": email.pk,
            "email_message_id": email.email_message_id,
            "email_thread_id": email_thread_id,
            "direction": email.direction,
        },
        tags=["reasoning_engine"],
    ) as ctx:
        # Log the Langfuse trace ID for debugging
        if ctx.trace:
            logger.info(
                "Langfuse AIOperationContext trace created",
                trace_id=ctx.trace.id,
            )
        # Configure observability for Langfuse tracking
        configure_observability()

        # Use the same method as the categorizer to get email text
        email_text = (
            await sync_to_async(email.as_display_string)(include_attachment_names=True)
            or ""
        )

        # Get attachments if available (Communication model may have related attachments)
        attachments = []

        # Get the Didero AI user for task creation
        # TODO: Make this configurable - allow specifying which user should receive tasks
        from didero.utils.utils import get_didero_ai_user

        user = await sync_to_async(get_didero_ai_user)(team)
        if not user:
            logger.warning(
                "Didero AI user not found for team",
                team_id=team_id,
            )

        # Phase 1: Context Building (pass-through for now)
        logger.info(
            "Starting context builder",
            team_id=team_id,
            email_id=email.pk,
            direction=email.direction,
        )

        context_deps = ContextBuilderDependencies(
            email=email,
            email_text=email_text,
            attachments=attachments,
            team_id=team_id,
        )

        context_result = await run_context_builder(context_deps, ctx)

        # Phase 2: Reasoning Agent
        logger.info(
            "Starting reasoning agent",
            team_id=team_id,
            context_available=context_result is not None,
        )

        reasoning_result = await run_reasoning_agent(
            context=context_result,
            team_id=team_id,
            email=email,
            user=user,
            team=team,
            ctx=ctx,
            disable_workflows=disable_side_effects,
        )

        # Build response
        return EmailReasoningEngineResponse(
            context=context_result,
            reasoning=reasoning_result,
            success=reasoning_result.success if reasoning_result else False,
            email_id=email.pk,
            team_id=team_id,
        )


async def run_context_builder(
    deps: ContextBuilderDependencies, ctx: AIOperationContext
) -> ContextBuilderResult:
    """
    Execute the Context Builder Agent.

    Currently implements a pass-through strategy where the email content
    is passed directly to the reasoning agent. In the future, this will
    extract references, entities, and build comprehensive context.

    Args:
        deps: Dependencies for Context Builder
        ctx: AI operation context for observability

    Returns:
        ContextBuilderResult
    """
    try:
        # For now, use NotImplementedError to trigger pass-through behavior
        # In the future, this will be:
        # with logfire.span("context_builder_execution", team_id=deps.team_id):
        #     result = await context_builder_agent.run(
        #         "Extract all references and context from this email",
        #         deps=deps
        #     )
        raise NotImplementedError("Context builder tools not yet implemented")

    except NotImplementedError as e:
        # Pass-through implementation: just pass email content as-is
        logger.info(
            "Context builder not implemented, using pass-through",
            team_id=deps.team_id,
            reason=str(e),
        )

        # Create minimal context with just email content
        context_result = ContextBuilderResult(
            content={
                "email": deps.email_text,
                "email_id": str(deps.email.pk),
            },
            references={},  # No extraction yet
            entities={},  # No database lookups yet
            thread=None,  # No thread analysis yet
            metadata={
                "pass_through": True,
                "reason": "Context builder not implemented",
            },
            tools_used=[],
            notes=["Using pass-through mode - context builder not yet implemented"],
        )

        return context_result

    except FallbackExceptionGroup as e:
        # All models failed
        logger.error(
            "All context builder models failed",
            team_id=deps.team_id,
            errors=[str(ex) for ex in e.exceptions],
        )

        # Create fallback context with error info
        context_result = ContextBuilderResult(
            content={"email": deps.email_text},
            references={},
            entities={},
            metadata={"error": "All models failed"},
            notes=[f"Context builder failed: {str(e)}"],
            tools_used=[],
        )

        return context_result

    except Exception as e:
        logger.error(
            "Unexpected error in context builder",
            team_id=deps.team_id,
            error=str(e),
            exc_info=True,
        )

        # Create minimal fallback context
        context_result = ContextBuilderResult(
            content={"email": deps.email_text},
            references={},
            entities={},
            metadata={"error": str(e)},
            notes=[f"Unexpected error: {str(e)}"],
            tools_used=[],
        )

        return context_result


async def run_reasoning_agent(
    context: ContextBuilderResult,
    team_id: int,
    email: Any = None,
    user: Optional[User] = None,
    team: Any = None,
    ctx: Optional[AIOperationContext] = None,
    disable_workflows: bool = False,
) -> ReasoningResult:
    """
    Execute the Reasoning Agent.

    Takes the context from Context Builder and determines what actions to take:
    - Trigger workflows for handled cases
    - Suggest database updates for field changes
    - Create tasks for unhandled cases

    Args:
        context: Output from Context Builder
        team_id: Team identifier
        email: Original email object (for task creation)
        user: User who will review tasks
        ctx: AI operation context for observability

    Returns:
        ReasoningResult
    """
    try:
        # Build dependencies for reasoning agent
        reasoning_deps = ReasoningDependencies(
            built_context=context.model_dump(),
            team_id=team_id,
            email=email,
            user=user,
            team=team,
            disable_workflows=disable_workflows,
        )

        # Build user prompt from context
        prompt = _build_reasoning_user_prompt(context)

        # Run reasoning agent with fallback support
        logger.info(
            "Starting pydantic-ai reasoning agent run",
            agent_name="reasoning_agent",
        )

        result = await reasoning_agent.run(
            prompt,
            deps=reasoning_deps,
            usage_limits=UsageLimits(request_limit=500),
        )

        logger.info(
            "Reasoning agent completed",
            team_id=team_id,
            workflows_triggered=len(result.output.workflows_triggered),
            database_updates=len(result.output.database_updates),
            unhandled_cases=len(result.output.unhandled_cases),
        )

        return result.output

    except FallbackExceptionGroup as e:
        # All reasoning models failed
        logger.error(
            "All reasoning models failed",
            team_id=team_id,
            errors=[str(ex) for ex in e.exceptions],
        )

        # Create error result
        reasoning_result = ReasoningResult(
            workflows_triggered=[],
            database_updates=[],
            unhandled_cases=[],
            summary="All reasoning models failed to process this email",
            success=False,
            errors=[str(ex) for ex in e.exceptions],
            raw_context=context.model_dump(),
        )

        return reasoning_result

    except Exception as e:
        logger.error(
            "Unexpected error in reasoning agent",
            team_id=team_id,
            error=str(e),
            exc_info=True,
        )

        # Create error result
        reasoning_result = ReasoningResult(
            workflows_triggered=[],
            database_updates=[],
            unhandled_cases=[],
            summary=f"Unexpected error: {str(e)}",
            success=False,
            errors=[str(e)],
            raw_context=context.model_dump(),
        )

        return reasoning_result


def _build_reasoning_user_prompt(context: ContextBuilderResult) -> str:
    """
    Build user prompt for the reasoning agent using Langfuse prompt template.

    The context already contains all extracted information (email content, references,
    entities, thread info, etc.) so we just pass the entire context to the template.

    Args:
        context: The complete context built by Context Builder

    Returns:
        Compiled user prompt string for reasoning agent
    """
    # Get the prompt template from Langfuse
    prompts_path = Path(__file__).parent / "agents" / "prompts_config.yaml"

    user_prompt_template = get_langfuse_prompt(
        prompts_path=prompts_path,
        prompt_key="reasoning_user_prompt",
    )

    # Compile the prompt with the entire context
    # The context includes: content, references, entities, thread, metadata, etc.
    context_json = context.model_dump_json(indent=2)

    compiled_prompt = str(
        user_prompt_template.compile(
            context=context_json,  # Pretty print the context for readability
        )
    )

    return compiled_prompt
