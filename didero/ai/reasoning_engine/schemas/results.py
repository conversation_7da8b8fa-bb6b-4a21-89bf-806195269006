"""
Result schemas for Reasoning Engine Agents

These Pydantic models define the structured outputs from agents.
"""

from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class ContextBuilderResult(BaseModel):
    """
    Output from the Context Builder Agent.

    Organized semantically by meaning, not by tool.
    Accumulates outputs from multiple tool calls into logical groups.
    """

    # All extracted text content
    content: Dict[str, str] = Field(
        default_factory=dict,
        description="Mapping of document_id -> extracted_text for all processed documents",
    )

    # All references (the "umbrella" structure from PRD)
    references: Dict[str, List[Dict[str, Any]]] = Field(
        default_factory=dict,
        description="References by type: {'po': [{number, confidence, source}], 'invoice': [...]}",
    )

    # All database context
    entities: Dict[str, Any] = Field(
        default_factory=dict,
        description="Database objects by reference: {'PO-123': {exists, object, related_objects}, ...}",
    )

    # Thread information
    thread: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Thread context with thread_id, related emails, and history",
    )

    # Metadata about processing
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Processing metadata: confidence scores, extraction methods, etc.",
    )

    # Tools that were called
    tools_used: List[str] = Field(
        default_factory=list,
        description="List of tools that contributed to this context",
    )

    # Processing notes
    notes: List[str] = Field(
        default_factory=list,
        description="Any important notes or warnings during processing",
    )


# Reasoning Result Types


class WorkflowTriggered(BaseModel):
    """Successfully handled by triggering an existing workflow."""

    workflow_type: str = Field(
        description="Type of workflow. Must be one of: 'po_creation', 'order_acknowledgement', 'shipment_processing', 'invoice_processing'"
    )
    workflow_params: Dict[str, Any] = Field(description="Parameters passed to workflow")
    success: bool = Field(description="Whether workflow was triggered successfully")
    workflow_id: Optional[str] = Field(
        default=None, description="ID returned from workflow system"
    )
    error_message: Optional[str] = Field(default=None, description="Error if failed")


class DatabaseUpdateSuggested(BaseModel):
    """Suggested database update requiring human approval."""

    model: str = Field(description="Database model/table to update")
    field: str = Field(description="Field to update")
    current_value: Any = Field(description="Current value in database")
    suggested_value: Any = Field(description="Suggested new value")
    reason: str = Field(description="Why this update is needed")
    confidence: float = Field(
        ge=0.0, le=1.0, description="Confidence in this suggestion"
    )
    reference_id: Optional[str] = Field(
        default=None, description="Related PO/Invoice number"
    )


class UnhandledCase(BaseModel):
    """
    Case that couldn't be handled with existing capabilities.
    Results in both a human task and a skills repository entry.
    """

    # Human task created
    task_created: Dict[str, Any] = Field(
        description="Human task details: {title, description, priority, context}"
    )

    # Future capability needed
    skill_suggestion: Dict[str, Any] = Field(
        description="Suggested capability: {pattern, required_tool, proposed_solution}"
    )

    # Context about why we couldn't handle it
    pattern_observed: str = Field(description="What pattern/scenario we encountered")
    reason_unhandled: str = Field(description="Why existing tools couldn't handle this")
    context_snapshot: Dict[str, Any] = Field(
        default_factory=dict, description="Relevant context that led to this case"
    )


class ReasoningResult(BaseModel):
    """
    Output from the Reasoning Agent.

    Categorizes all actions into handled (workflows/DB updates) or unhandled (tasks + skills).
    """

    # Successfully handled cases
    workflows_triggered: List[WorkflowTriggered] = Field(
        default_factory=list, description="Workflows that were triggered"
    )

    database_updates: List[DatabaseUpdateSuggested] = Field(
        default_factory=list,
        description="Database updates suggested (need human approval)",
    )

    # Cases we couldn't handle
    unhandled_cases: List[UnhandledCase] = Field(
        default_factory=list,
        description="Cases requiring human intervention and future capabilities",
    )

    # Overall summary
    summary: str = Field(description="Human-readable summary of what was done")

    success: bool = Field(
        description="Overall success (true even if some cases were unhandled)"
    )

    # Processing metadata
    confidence_score: float = Field(
        default=0.0, ge=0.0, le=1.0, description="Overall confidence in decisions made"
    )

    processing_time_ms: Optional[int] = Field(default=None)

    errors: List[str] = Field(
        default_factory=list, description="Any errors encountered during processing"
    )

    # For backwards compatibility or debugging
    raw_context: Optional[Dict[str, Any]] = Field(
        default=None, description="Original context from ContextBuilder if needed"
    )


class EmailReasoningEngineResponse(BaseModel):
    """
    Response schema for the email reasoning engine.

    This is the main response returned by email_reasoning_engine() function,
    containing both context building and reasoning results.
    """

    # Context from the Context Builder
    context: Optional[ContextBuilderResult] = Field(
        default=None,
        description="Context built by the Context Builder agent",
    )

    # Reasoning results
    reasoning: Optional[ReasoningResult] = Field(
        default=None,
        description="Results from the Reasoning agent",
    )

    # Overall status
    success: bool = Field(
        default=False,
        description="Overall success of the reasoning engine processing",
    )

    # Identifiers
    email_id: Union[int, str] = Field(
        description="Primary key of the processed Communication object",
    )

    team_id: int = Field(
        description="ID of the team that owns the email",
    )
