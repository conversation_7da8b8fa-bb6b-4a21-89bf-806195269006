"""
Type definitions for Reasoning Engine tools.

Contains type aliases, literals, and Pydantic models used by tool functions.
"""

from typing import List, Literal, Optional

from pydantic import BaseModel, Field

# Manual review task types
CategoryType = Literal[
    "pricing",
    "shipping",
    "order_status",
    "approval",
    "clarification",
    "notification",
    "general",
]

UrgencyType = Literal[
    "high",  # Needs immediate attention
    "medium",  # Should be handled soon
    "low",  # Can wait, informational
]


class ManualReviewTaskInput(BaseModel):
    """Input parameters for creating a manual review task."""

    supplier_name: Optional[str] = Field(
        None, description="Name of the supplier involved"
    )
    category: CategoryType = Field(..., description="Type of request being handled")
    requires_action: bool = Field(
        ..., description="Whether buyer action is needed vs informational only"
    )
    task_description: str = Field(
        ..., description="Clear, actionable description for the task title"
    )
    situation_summary: str = Field(
        ..., description="Brief context explaining the situation (2-3 sentences)"
    )
    po_numbers: Optional[List[str]] = Field(
        None, description="List of PO numbers referenced"
    )
    invoice_numbers: Optional[List[str]] = Field(
        None, description="List of invoice numbers referenced"
    )
    other_references: Optional[str] = Field(
        None, description="Other reference numbers or identifiers"
    )
    urgency_indicator: UrgencyType = Field("medium", description="Task urgency level")


class ManualReviewTaskCreationResult(BaseModel):
    """Return type for create_manual_review_task."""

    task_id: Optional[str] = None
    success: bool
    task_type: Optional[str] = None
    assigned_to: Optional[str] = None
    error: Optional[str] = None


class SkillsRepositoryResult(BaseModel):
    """Return type for add_to_skills_repository."""

    skill_id: Optional[str] = None
    similar_patterns: int = 0
    success: bool
    error: Optional[str] = None


# Workflow trigger types
WorkflowType = Literal[
    "po_creation",
    "order_acknowledgement",
    "shipment_processing",
    "invoice_processing",
]


class WorkflowTriggerResult(BaseModel):
    """Common return type for all workflow triggers."""

    success: bool
    workflow_id: Optional[str] = None  # Temporal workflow ID for tracking
    workflow_type: str
    message: Optional[str] = None  # Human-readable status message
    error: Optional[str] = None  # Error details if failed
