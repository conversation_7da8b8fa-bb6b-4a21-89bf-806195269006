"""
Dependencies for Reasoning Engine Agents

These dataclasses provide data, services, and configuration to agents during execution.
Dependencies are passed to tools via RunContext and persist throughout the agent run.

NOTE: Add or remove fields based on actual requirements as we implement tools.
      Start minimal and expand as needed.
"""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from didero.suppliers.models import Communication
from didero.users.models import User


@dataclass
class ContextBuilderDependencies:
    """
    Dependencies for the Context Builder Agent.

    Provides input data and services needed by context-building tools.
    All tools can access these via ctx.deps.
    """

    # Input data - accessible to all tools
    email: Communication
    email_text: str
    attachments: List[Dict[str, Any]]
    team_id: int

    # TODO: Add services as needed
    # db_connection: Optional[Any] = None
    # storage_client: Optional[Any] = None  # For document storage
    # ocr_client: Optional[Any] = None  # For document extraction

    # TODO: Add configuration
    # extraction_config: Optional[Dict[str, Any]] = None
    # reference_patterns: Optional[Dict[str, str]] = None


@dataclass
class ReasoningDependencies:
    """
    Dependencies for the Reasoning Agent.

    Provides context and services needed by action tools.
    """

    # Context from Context Builder
    built_context: Dict[str, Any]  # Output from ContextBuilderResult
    team_id: int

    # Original data (for task creation)
    email: Optional[Communication] = None
    user: Optional[User] = None
    team: Optional[Any] = None  # Team object to avoid FK lookups in tools

    # Configuration
    workflow_config: Optional[Dict[str, Any]] = None
    tolerance_config: Optional[Dict[str, Any]] = None
    disable_workflows: bool = False

    # TODO: Add action services as needed
    # temporal_client: Optional[Any] = None  # For workflow execution
    # task_service: Optional[Any] = None  # For creating human tasks
    # notification_service: Optional[Any] = None
