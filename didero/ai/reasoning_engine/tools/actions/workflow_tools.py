"""
Workflow triggering tools for the Reasoning Agent.

These tools trigger various workflows based on reasoning decisions.
Each workflow has its own trigger function for explicit agent control.
"""

import structlog
from asgiref.sync import sync_to_async
from pydantic_ai import RunContext

from ...schemas.dependencies import ReasoningDependencies
from ...schemas.tool_types import WorkflowTriggerResult as AIWorkflowTriggerResult

logger = structlog.get_logger(__name__)


async def _trigger_workflow_base(
    ctx: RunContext[ReasoningDependencies],
    workflow_type_value: str,
    workflow_trigger_value: str,
    workflow_name: str,
    workflow_type_key: str,
) -> AIWorkflowTriggerResult:
    """
    Base function for triggering workflows with common logic.

    Args:
        ctx: Run context with email dependency
        workflow_type_value: The WorkflowType enum value (e.g., WorkflowType.PURCHASE_ORDER_CREATION.value)
        workflow_trigger_value: The WorkflowTrigger enum value (e.g., WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value)
        workflow_name: Human-readable workflow name for logging and messages
        workflow_type_key: Short key for workflow type in response (e.g., "po_creation")

    Returns:
        Workflow trigger result with ID and status
    """
    logger.info(
        f"Triggering {workflow_name} workflow",
        workflow_type=workflow_type_value,
        workflow_trigger=workflow_trigger_value,
        has_email=bool(ctx.deps.email),
        has_team=bool(ctx.deps.team),
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
    )

    if getattr(ctx.deps, "disable_workflows", False):
        logger.info(
            f"Skipping {workflow_name} workflow due to simulation mode",
            workflow_type=workflow_type_value,
            team_id=ctx.deps.team_id,
        )
        return AIWorkflowTriggerResult(
            success=True,
            workflow_id=None,
            workflow_type=workflow_type_key,
            message=f"{workflow_name} workflow skipped (simulation mode)",
            error=None,
        )

    if not ctx.deps.email:
        logger.error(f"No email context for {workflow_name} workflow")
        return AIWorkflowTriggerResult(
            success=False,
            workflow_id=None,
            workflow_type=workflow_type_key,
            message=None,
            error=f"Email context required for {workflow_name} workflow",
        )

    if not ctx.deps.team:
        logger.error(f"No team context for {workflow_name} workflow")
        return AIWorkflowTriggerResult(
            success=False,
            workflow_id=None,
            workflow_type=workflow_type_key,
            message=None,
            error=f"Team context required for {workflow_name} workflow",
        )

    try:
        from didero.telemetry.workflow_metrics import (
            record_email_workflow_trigger,
            record_workflow_skip,
        )
        from didero.workflows.utils import (
            EmailWorkflowContext,
            trigger_workflow_if_exists_with_status,
        )

        logger.info(
            f"AI Agent triggering {workflow_name} Workflow",
            team_id=ctx.deps.team_id,
            email_id=ctx.deps.email.pk,
        )

        # Wrap synchronous functions for async context
        trigger_sync = sync_to_async(trigger_workflow_if_exists_with_status)

        logger.info(
            f"About to trigger {workflow_name} workflow",
            workflow_type=workflow_type_value,
            workflow_trigger=workflow_trigger_value,
            team_id=ctx.deps.team_id,
            email_id=ctx.deps.email.pk,
        )

        result = await trigger_sync(
            workflow_type_value,
            workflow_trigger_value,
            ctx.deps.team,
            context_object=EmailWorkflowContext(email=ctx.deps.email),
        )

        logger.info(
            f"Workflow trigger result for {workflow_name}",
            triggered=result.triggered,
            workflow_found=result.workflow_found,
            temporal_id=result.temporal_id,
            error_message=result.error_message,
            team_id=ctx.deps.team_id,
            email_id=ctx.deps.email.pk,
        )

        # Record telemetry metrics
        if result.triggered:
            logger.info(
                f"Successfully triggered {workflow_name} Workflow",
                team_id=ctx.deps.team_id,
                email_id=ctx.deps.email.pk,
                temporal_id=result.temporal_id,
            )
            # Use sync_to_async for the telemetry recording
            record_trigger_sync = sync_to_async(record_email_workflow_trigger)
            await record_trigger_sync(
                team_id=str(ctx.deps.team_id),
                workflow_type=workflow_type_value,
                trigger=workflow_trigger_value,
                category="reasoning_agent_triggered",
                email_id=str(ctx.deps.email.pk),
            )
        else:
            reason = (
                "workflow_not_configured"
                if not result.workflow_found
                else f"trigger_failed: {result.error_message}"
            )
            logger.info(
                f"{workflow_name} workflow not triggered",
                team_id=ctx.deps.team_id,
                email_id=ctx.deps.email.pk,
                reason=reason,
            )
            record_skip_sync = sync_to_async(record_workflow_skip)
            await record_skip_sync(
                team_id=str(ctx.deps.team_id),
                workflow_type=workflow_type_value,
                reason=reason,
                source="reasoning_agent",
                email_id=str(ctx.deps.email.pk),
            )

        # Create the result to return
        ai_result = AIWorkflowTriggerResult(
            success=result.triggered,
            workflow_id=result.temporal_id,
            workflow_type=workflow_type_key,
            message=f"{workflow_name} workflow started"
            if result.triggered
            else "Workflow not configured for team",
            error=result.error_message if not result.triggered else None,
        )

        logger.info(
            f"Returning AIWorkflowTriggerResult for {workflow_name}",
            success=ai_result.success,
            workflow_id=ai_result.workflow_id,
            workflow_type=ai_result.workflow_type,
            message=ai_result.message,
            error=ai_result.error,
            team_id=ctx.deps.team_id,
            email_id=ctx.deps.email.pk,
        )

        return ai_result

    except Exception as e:
        logger.error(
            f"Failed to trigger {workflow_name} workflow",
            team_id=ctx.deps.team_id,
            email_id=ctx.deps.email.pk if ctx.deps.email else None,
            error=str(e),
        )
        return AIWorkflowTriggerResult(
            success=False,
            workflow_id=None,
            workflow_type=workflow_type_key,
            message=None,
            error=f"Failed to trigger workflow: {str(e)}",
        )


async def trigger_po_creation_workflow(
    ctx: RunContext[ReasoningDependencies],
) -> AIWorkflowTriggerResult:
    """
    Triggers the purchase order creation workflow.

    This workflow extracts PO details from the email and creates a purchase order.
    The workflow handles supplier matching, validation, and PO creation.

    Args:
        ctx: Run context with email dependency

    Returns:
        Workflow trigger result with ID and status
    """
    from didero.workflows.schemas import WorkflowTrigger, WorkflowType

    logger.info(
        "trigger_po_creation_workflow called, about to call _trigger_workflow_base",
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    result = await _trigger_workflow_base(
        ctx=ctx,
        workflow_type_value=WorkflowType.PURCHASE_ORDER_CREATION.value,
        workflow_trigger_value=WorkflowTrigger.ON_PURCHASE_ORDER_EMAIL_RECEIVED.value,
        workflow_name="PO Creation",
        workflow_type_key="po_creation",
    )

    logger.info(
        "trigger_po_creation_workflow returning result",
        success=result.success,
        workflow_id=result.workflow_id,
        message=result.message,
        error=result.error,
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    return result


async def trigger_order_acknowledgement_workflow(
    ctx: RunContext[ReasoningDependencies],
) -> AIWorkflowTriggerResult:
    """
    Triggers the order acknowledgement workflow.

    This workflow processes supplier confirmations of purchase orders,
    updating order status and line item details.

    Args:
        ctx: Run context with email dependency

    Returns:
        Workflow trigger result with ID and status
    """
    from didero.workflows.schemas import WorkflowTrigger, WorkflowType

    logger.info(
        "trigger_order_acknowledgement_workflow called, about to call _trigger_workflow_base",
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    result = await _trigger_workflow_base(
        ctx=ctx,
        workflow_type_value=WorkflowType.PURCHASE_ORDER_ACKNOWLEDGEMENT.value,
        workflow_trigger_value=WorkflowTrigger.ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED.value,
        workflow_name="Order Acknowledgement",
        workflow_type_key="order_acknowledgement",
    )

    logger.info(
        "trigger_order_acknowledgement_workflow returning result",
        success=result.success,
        workflow_id=result.workflow_id,
        message=result.message,
        error=result.error,
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    return result


async def trigger_shipment_processing_workflow(
    ctx: RunContext[ReasoningDependencies],
) -> AIWorkflowTriggerResult:
    """
    Triggers the shipment processing workflow.

    This workflow processes shipment notifications and updates,
    tracking deliveries and updating order fulfillment status.

    Args:
        ctx: Run context with email dependency

    Returns:
        Workflow trigger result with ID and status
    """
    from didero.workflows.schemas import WorkflowTrigger, WorkflowType

    logger.info(
        "trigger_shipment_processing_workflow called, about to call _trigger_workflow_base",
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    result = await _trigger_workflow_base(
        ctx=ctx,
        workflow_type_value=WorkflowType.PURCHASE_ORDER_SHIPPED.value,
        workflow_trigger_value=WorkflowTrigger.ON_ORDER_SHIPPED_EMAIL_RECEIVED.value,
        workflow_name="Shipment Processing",
        workflow_type_key="shipment_processing",
    )

    logger.info(
        "trigger_shipment_processing_workflow returning result",
        success=result.success,
        workflow_id=result.workflow_id,
        message=result.message,
        error=result.error,
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    return result


async def trigger_invoice_processing_workflow(
    ctx: RunContext[ReasoningDependencies],
) -> AIWorkflowTriggerResult:
    """
    Triggers the invoice processing workflow.

    This workflow processes supplier invoices, matching them with purchase orders
    and preparing them for payment processing.

    Args:
        ctx: Run context with email dependency

    Returns:
        Workflow trigger result with ID and status
    """
    from didero.workflows.schemas import WorkflowTrigger, WorkflowType

    logger.info(
        "trigger_invoice_processing_workflow called, about to call _trigger_workflow_base",
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    result = await _trigger_workflow_base(
        ctx=ctx,
        workflow_type_value=WorkflowType.INVOICE_PROCESSING.value,
        workflow_trigger_value=WorkflowTrigger.ON_INVOICE_EMAIL_RECEIVED.value,
        workflow_name="Invoice Processing",
        workflow_type_key="invoice_processing",
    )

    logger.info(
        "trigger_invoice_processing_workflow returning result",
        success=result.success,
        workflow_id=result.workflow_id,
        message=result.message,
        error=result.error,
        email_id=ctx.deps.email.pk if ctx.deps.email else None,
        team_id=ctx.deps.team_id,
    )

    return result
