"""
Django admin interface for viewing evaluation results.

This provides a user-friendly interface to:
- View evaluation runs and their scores
- Drill into individual test results
- See additional actions discovered
- Track evaluation history over time
"""

import json
from typing import Iterable, List, Tuple

from django.contrib import admin
from django.utils.html import format_html, format_html_join


from .models import EvaluationResult, EvaluationRun


@admin.register(EvaluationRun)
class EvaluationRunAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    """
    Admin interface for evaluation runs.

    Shows the list of all evaluation runs with their scores and allows
    drilling into individual results.
    """

    list_display = [
        "uuid",
        "dataset_name",
        "score_display",
        "passed_cases",
        "total_cases",
        "created_at",
    ]
    list_filter = ["system_tested", "dataset_name", "created_at"]
    ordering = ["-created_at"]
    readonly_fields = ["uuid", "started_at", "completed_at", "configuration_display"]

    def score_display(self, obj: EvaluationRun) -> str:
        """Display score with color coding based on performance."""
        if obj.overall_score >= 0.8:
            color = "green"
        elif obj.overall_score >= 0.5:
            color = "orange"
        else:
            color = "red"

        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1%}</span>',
            color,
            obj.overall_score,
        )

    score_display.short_description = "Score"  # type: ignore[attr-defined]

    def configuration_display(self, obj: EvaluationRun) -> str:
        """Display configuration as formatted JSON."""
        return format_html("<pre>{}</pre>", json.dumps(obj.configuration, indent=2))

    configuration_display.short_description = "Configuration"  # type: ignore[attr-defined]


@admin.register(EvaluationResult)
class EvaluationResultAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    """
    Admin interface for individual test results.

    Shows each test case result with its score and any additional
    actions that were discovered.
    """

    DIMENSION_LABELS = {
        "workflows": "Workflows",
        "database_updates": "Database Updates",
        "human_tasks": "Human Tasks",
    }

    list_display = [
        "test_name",
        "status_display",
        "score",
        "has_additional_actions",
        "execution_time_display",
        "run",
    ]
    list_filter = ["passed", "test_name", "run__dataset_name"]
    search_fields = ["test_name", "email_text", "error_message"]
    readonly_fields = [
        "test_name",
        "email_text_display",
        "expected_category",
        "formatted_output",
        "formatted_additional_actions",
        "execution_time_ms",
        "error_message",
    ]

    def status_display(self, obj: EvaluationResult) -> str:
        """Display pass/fail status with emoji and color."""
        if obj.error_message:
            return format_html('<span style="color: red;">❌ Error</span>')

        if obj.passed:
            return format_html('<span style="color: green;">✅ Passed</span>')
        else:
            return format_html('<span style="color: red;">❌ Failed</span>')

    status_display.short_description = "Status"  # type: ignore[attr-defined]

    def has_additional_actions(self, obj: EvaluationResult) -> bool:
        """Check if any additional actions were discovered."""
        data = obj.additional_actions_found or {}
        if not isinstance(data, dict):
            return False

        for key in self.DIMENSION_LABELS:
            metrics = data.get(key) or {}
            if not isinstance(metrics, dict):
                metrics = {}
            has_metrics = any(
                metrics.get(field)
                for field in ("expected", "matched", "missing", "unexpected")
            ) or metrics.get("missing_items") or metrics.get("unexpected_items")

            if has_metrics:
                return True

        overall = data.get("overall", {})
        if overall.get("unexpected") or overall.get("missing"):
            return True

        return False

    has_additional_actions.boolean = True  # type: ignore[attr-defined]
    has_additional_actions.short_description = "Extra Actions Found"  # type: ignore[attr-defined]

    def execution_time_display(self, obj: EvaluationResult) -> str:
        """Display execution time in a readable format."""
        if obj.execution_time_ms:
            if obj.execution_time_ms < 1000:
                return f"{obj.execution_time_ms}ms"
            else:
                return f"{obj.execution_time_ms / 1000:.1f}s"
        return "-"

    execution_time_display.short_description = "Time"  # type: ignore[attr-defined]

    def email_text_display(self, obj: EvaluationResult) -> str:
        """Display email text with proper formatting."""
        return format_html(
            '<pre style="white-space: pre-wrap;">{}</pre>', obj.email_text
        )

    email_text_display.short_description = "Email Text"  # type: ignore[attr-defined]

    def formatted_output(self, obj: EvaluationResult) -> str:
        """Display the actual output as formatted JSON."""
        return format_html("<pre>{}</pre>", json.dumps(obj.actual_output, indent=2))

    formatted_output.short_description = "Actual Output"  # type: ignore[attr-defined]

    def formatted_additional_actions(self, obj: EvaluationResult) -> str:
        """
        Display additional actions in a readable format.

        This is the most important field - shows what extra value
        the reasoning engine discovered beyond basic categorization.
        """
        if not self.has_additional_actions(obj):
            return "None"
        data = obj.additional_actions_found or {}
        if not isinstance(data, dict):
            return "None"
        sections: List[str] = []

        overall = data.get("overall") or {}
        if not isinstance(overall, dict):
            overall = {}
        if overall:
            sections.append(
                format_html(
                    '<div><strong>Overall</strong>: matched {}/{}'
                    "{}" "{}" '</div>',
                    overall.get("matched", 0),
                    overall.get("expected", 0),
                    self._format_metric_suffix("missing", overall.get("missing", 0)),
                    self._format_metric_suffix(
                        "unexpected", overall.get("unexpected", 0)
                    ),
                )
            )

        expected_data = data.get("expected") or {}
        if not isinstance(expected_data, dict):
            expected_data = {}
        actual_data = data.get("actual") or {}
        if not isinstance(actual_data, dict):
            actual_data = {}

        for key, label in self.DIMENSION_LABELS.items():
            metrics = data.get(key) or {}
            if not isinstance(metrics, dict):
                metrics = {}

            expected_items = expected_data.get(key)
            if not isinstance(expected_items, list):
                expected_items = []

            missing_items = metrics.get("missing_items") or []
            if not isinstance(missing_items, list):
                missing_items = []
            unexpected_items = metrics.get("unexpected_items") or []
            if not isinstance(unexpected_items, list):
                unexpected_items = []

            rows = self._build_action_rows(obj.actual_output, key)

            section = self._render_dimension(
                label,
                metrics,
                rows,
                expected_items,
                missing_items,
                unexpected_items,
            )
            if section:
                sections.append(section)

        if not sections:
            return "None"

        content = format_html_join("", "{}", ((section,) for section in sections))
        return format_html(
            '<div style="font-family: monospace;">{}</div>',
            content,
        )

    formatted_additional_actions.short_description = "Additional Actions Found"  # type: ignore[attr-defined]

    def _format_metric_suffix(self, label: str, value: int) -> str:
        """Render a " | Label: value" suffix when value is positive."""
        if not value:
            return ""
        return format_html(" | {}: {}", label.capitalize(), value)

    def _render_dimension(
        self,
        label: str,
        metrics: dict,
        rows: List[Tuple[str, str, str]],
        expected_items: Iterable[str],
        missing_items: Iterable[str],
        unexpected_items: Iterable[str],
    ) -> str:
        """Render summary, action table, and expectation deltas for one dimension."""

        has_any = any(
            [
                rows,
                list(expected_items or []),
                list(missing_items or []),
                list(unexpected_items or []),
                metrics.get("expected"),
                metrics.get("matched"),
                metrics.get("missing"),
                metrics.get("unexpected"),
            ]
        )

        if not has_any:
            return ""

        summary = format_html(
            "<div><strong>{}</strong>: {}/{} matched{missing}{unexpected}</div>",
            label,
            metrics.get("matched", 0),
            metrics.get("expected", 0),
            missing=self._format_metric_suffix("missing", metrics.get("missing", 0)),
            unexpected=self._format_metric_suffix(
                "unexpected", metrics.get("unexpected", 0)
            ),
        )

        table = self._render_actions_table(rows) if rows else ""

        extras = list(
            filter(
                None,
                [
                    self._render_simple_list("Expected", expected_items),
                    self._render_simple_list("Missing", missing_items),
                    self._render_simple_list("Unexpected", unexpected_items),
                ],
            )
        )

        return format_html_join(
            "",
            "{}",
            ((section,) for section in [summary, table, *extras] if section),
        )

    def _render_actions_table(self, rows: List[Tuple[str, str, str]]) -> str:
        """Render an HTML table summarizing individual actions."""
        if not rows:
            return ""

        header = format_html(
            "<tr><th style='text-align:left;'>Action</th><th style='text-align:left;'>Details</th><th style='text-align:left;'>Outcome</th></tr>"
        )
        body = format_html_join("", "<tr><td>{}</td><td>{}</td><td>{}</td></tr>", rows)
        return format_html(
            "<table style='border-collapse: collapse; width: 100%; margin: 6px 0;'>"
            "{}{}"
            "</table>",
            header,
            body,
        )

    def _render_simple_list(self, title: str, items: Iterable[str]) -> str:
        values = [item for item in (items or []) if item]
        if not values:
            return ""
        return format_html(
            "<div>{}: <ul>{}</ul></div>",
            title,
            format_html_join("", "<li>{}</li>", ((value,) for value in values)),
        )

    def _build_action_rows(
        self,
        raw_output: dict,
        dimension: str,
    ) -> List[Tuple[str, str, str]]:
        """Build table rows for the requested dimension."""

        rows: List[Tuple[str, str, str]] = []
        if not isinstance(raw_output, dict):
            raw_output = {}

        if dimension == "workflows":
            for wf in raw_output.get("workflows_triggered") or []:
                if isinstance(wf, dict):
                    wf_type = wf.get("workflow_type") or "Unknown"
                    params = wf.get("workflow_params") or {}
                    success = wf.get("success", False)
                    workflow_id = wf.get("workflow_id")
                    error = wf.get("error_message") or wf.get("message")
                else:
                    wf_type = getattr(wf, "workflow_type", "Unknown")
                    params = getattr(wf, "workflow_params", {})
                    success = getattr(wf, "success", False)
                    workflow_id = getattr(wf, "workflow_id", None)
                    error = getattr(wf, "error_message", None)

                details = self._format_key_values(params)
                status = self._format_status(success, workflow_id, error)
                rows.append((wf_type, details, status))

        elif dimension == "database_updates":
            for update in raw_output.get("database_updates") or []:
                if isinstance(update, dict):
                    model = update.get("model")
                    field = update.get("field")
                    current = update.get("current_value")
                    suggested = update.get("suggested_value")
                    reason = update.get("reason")
                    confidence = update.get("confidence")
                else:
                    model = getattr(update, "model", None)
                    field = getattr(update, "field", None)
                    current = getattr(update, "current_value", None)
                    suggested = getattr(update, "suggested_value", None)
                    reason = getattr(update, "reason", None)
                    confidence = getattr(update, "confidence", None)

                if not model or not field:
                    continue

                name = f"{model}.{field}"
                details = self._render_update_details(current, suggested, reason, confidence)
                rows.append((name, details, "Pending approval"))

        elif dimension == "human_tasks":
            for case in raw_output.get("unhandled_cases") or []:
                if isinstance(case, dict):
                    task = case.get("task_created") or {}
                    title = task.get("title") or case.get("reason_unhandled") or "Task"
                    summary = task.get("description") or case.get("pattern_observed")
                    urgency = task.get("urgency") or task.get("urgency_indicator")
                else:
                    task = getattr(case, "task_created", {}) or {}
                    title = task.get("title") if isinstance(task, dict) else getattr(task, "title", "Task")
                    if not title:
                        title = getattr(case, "reason_unhandled", "Task")
                    summary = (
                        task.get("description")
                        if isinstance(task, dict)
                        else getattr(task, "description", None)
                    ) or getattr(case, "pattern_observed", None)
                    urgency = (
                        task.get("urgency")
                        if isinstance(task, dict)
                        else getattr(task, "urgency", None)
                    ) or getattr(case, "urgency_indicator", None)

                details = self._render_task_details(summary, urgency)
                rows.append((title or "Task", details, "Needs review"))

        return rows

    def _format_key_values(self, data: dict) -> str:
        if not isinstance(data, dict) or not data:
            return "—"
        return format_html(
            "<ul>{}</ul>",
            format_html_join(
                "",
                "<li><strong>{}:</strong> {}</li>",
                ((key, value) for key, value in data.items()),
            ),
        )

    def _format_status(self, success: bool, workflow_id: str | None, error: str | None) -> str:
        if success:
            extra = f" (ID {workflow_id})" if workflow_id else ""
            return format_html("<span style='color: green;'>✅ Success{}</span>", extra)
        if error:
            return format_html("<span style='color: red;'>❌ Failed: {}</span>", error)
        return "—"

    def _render_update_details(
        self,
        current: object,
        suggested: object,
        reason: str | None,
        confidence: object,
    ) -> str:
        items = [
            format_html("<strong>Current:</strong> {}", current) if current is not None else "",
            format_html("<strong>Suggested:</strong> {}", suggested)
            if suggested is not None
            else "",
            format_html("<strong>Reason:</strong> {}", reason) if reason else "",
            format_html("<strong>Confidence:</strong> {}", confidence)
            if confidence is not None
            else "",
        ]
        contents = [item for item in items if item]
        if not contents:
            return "—"
        return format_html_join("<br>", "{}", ((item,) for item in contents))

    def _render_task_details(self, summary: str | None, urgency: str | None) -> str:
        parts = []
        if summary:
            parts.append(format_html("<div><strong>Summary:</strong> {}</div>", summary))
        if urgency:
            parts.append(format_html("<div><strong>Urgency:</strong> {}</div>", urgency))
        if not parts:
            return "—"
        return format_html_join("", "{}", ((part,) for part in parts))
