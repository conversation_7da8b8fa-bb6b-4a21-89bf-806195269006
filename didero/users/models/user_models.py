from datetime import <PERSON><PERSON><PERSON>
from typing import TYPE_CHECKING
from uuid import uuid4

import structlog
from auditlog.registry import auditlog
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.utils.crypto import get_random_string
from django.utils.http import url_has_allowed_host_and_scheme
from rest_framework.authtoken.models import Token

from didero.models import BaseModel
from didero.settings.common import IGNORED_DOMAINS
from didero.users.models.team_models import Team, TeamDomain
from didero.users.utils.team_setting_utils import create_default_user_settings
from didero.utils.utils import (
    domain_from_url,
    get_didero_ai_user_email,
    get_domain_from_email,
    is_admin_email,
)

if TYPE_CHECKING:
    from didero.users.models.team_models import Team


logger = structlog.get_logger(__name__)

"""
Adding a new model? Make sure to explicitly import it in didero.users.models.__init__.py !
This is because django requires all models to be imported there, but we wanted to
organize our code into multiple files, and so defined our model classes in those
other files. Plus, in order to avoid circular imports, our default is to explicitly import
from these subfiles.
"""


def get_random_token():
    return get_random_string(32)


class User(AbstractUser):
    USERNAME_FIELD = "plain_email"  # Phase 2: Use plain_email for authentication
    EMAIL_FIELD = "plain_email"
    REQUIRED_FIELDS = []
    is_demo_user = models.BooleanField(default=False)
    uuid = models.UUIDField(unique=True, default=uuid4)
    username = None

    email = None
    first_name = None
    last_name = None
    phone = None

    # Plain text fields (Phase 2) - Primary fields
    plain_email = models.EmailField(null=True, blank=True, unique=True)
    plain_first_name = models.CharField(max_length=30, null=True, blank=True)
    plain_last_name = models.CharField(max_length=150, null=True, blank=True)
    plain_phone = models.CharField(max_length=15, null=True, blank=True)

    # Related names/fields:
    teams: models.QuerySet["Team"]

    def save(self, *args, **kwargs):
        is_new = self.pk is None

        super().save(*args, **kwargs)
        # Do some custom actions for new users, but not for ops/contractors+ accounts
        if is_new and not is_admin_email(self.plain_email):
            self.assign_user_to_team()
            # Do not allow password login:
            # We use magic links for users, and django login for operators
            self.set_unusable_password()
            create_default_user_settings(self)

    def __str__(self):
        return self.plain_email or ""

    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between."""
        full_name = (
            f"{self.plain_first_name or ''} {self.plain_last_name or ''}".strip()
        )
        return full_name or self.plain_email or ""

    def clean(self):
        if self.plain_email:
            self.plain_email = self.plain_email.lower()

        if self.plain_phone:
            self.plain_phone = self.plain_phone.replace(" ", "").replace("+", "")

    # Read-only passthrough properties for legacy attribute access
    # Accessing user.email/first_name/last_name returns the plain_* equivalents
    @property
    def email(self) -> str:  # type: ignore[override]
        return self.plain_email or ""

    @property
    def first_name(self) -> str:  # type: ignore[override]
        return self.plain_first_name or ""

    @property
    def last_name(self) -> str:  # type: ignore[override]
        return self.plain_last_name or ""

    @property
    def display_name(self):
        if self.plain_first_name:
            return f"{self.plain_first_name or ''} {self.plain_last_name or ''}".strip()
        return self.plain_email or ""

    """
    The main entry points for user creation are via email signup on the website, and
    from the django admin page. If we create users anywhere else, we should remember
    to call this function there too.
    This function handles choosing which team to add a user to. If the user has a generic domain
    (e.g. @gmail.com), then we have no info on where to add them, so we create a new team
    just for them.
    If the user has a specific company domain, we check if that company has a team already.
    1. If so, add this user to that team.
    2. If not:
        a. Create a new team for that company.
        b. Create a Didero admin account in that team.
    """

    def assign_user_to_team(self):
        # When we generate demo data, we flag using is_demo_user
        # If so, then when we create the team, mark the team
        # as a demo team.
        # But by default, a normal team is not marked as dmeo
        logger.info(f"Adding user {self.uuid} to a team...")
        domain = get_domain_from_email(self.plain_email)
        logger.info(f"Found domain {domain}...")
        # If e.g. @gmail - not a company-specific email.
        if domain in IGNORED_DOMAINS:
            logger.info(f"Domain {domain} is generic; creating new Team for user...")
            team = Team.objects.create(is_demo_team=self.is_demo_user)
            team.name = f"Team {team.pk}"
            team.save()
        else:
            team_domain = TeamDomain.objects.filter(domain=domain).first()
            if team_domain:
                team = team_domain.team
                logger.info(
                    f"Domain {domain} already exists; adding user {self.uuid} to Team {team.pk}."
                )
            else:
                logger.info(
                    f"Domain {domain} is new; creating new Team and TeamDomain..."
                )
                team = Team.objects.create(
                    is_demo_team=self.is_demo_user,
                    name=domain,
                )
                team_domain_object = TeamDomain.objects.create(team=team, domain=domain)
                logger.info(
                    f"Created new Team {team.name} (pk: {team.pk}) and TeamDomain {team_domain_object.pk} for domain {domain}..."
                )
                # Team setup. This is the only place that team creation should happen,
                # and so all the handling for it happens here
                logger.info(
                    f"User {self.uuid} was the first member of domain {domain}; created new team."
                )
                logger.info(f"Creating Didero admin user for team {team.pk}...")
                didero_admin_user = User.objects.create(
                    plain_email=get_didero_ai_user_email(team),
                    plain_first_name="Didero",
                    plain_last_name="AI",
                    is_demo_user=self.is_demo_user,
                )
                team.users.add(didero_admin_user)
                create_default_user_settings(didero_admin_user)
                logger.info(
                    f"Successfully created Didero admin user for team {domain}.."
                )
                logger.info(f"Creating default item config for team {team.pk}")
                # avoid circular import
                from didero.items.models import ItemFieldsDisplayConfig

                ItemFieldsDisplayConfig.objects.create(team=team)

                logger.info(f"Creating default team settings for team {team.pk}")

                # create team default settings; have to add the import here to avoid circular dependencies
                from didero.users.utils.team_setting_utils import (
                    create_default_team_settings,
                )

                create_default_team_settings(team)
        team.users.add(self)
        logger.info(f"User {self.uuid} added to team {team.name} (pk: {team.pk})...")

    def get_notifications(self, team: Team, unread_only: bool = False):
        # we'll be able to prefetch the GenericForeignKeys when we're on django 5.0+
        # https://docs.djangoproject.com/en/5.0/ref/contrib/contenttypes/#genericprefetch
        qs = self.notifications.filter(team=team)
        if unread_only:
            qs = qs.filter(read_at__isnull=True)
        return qs.order_by("-created_at")


auditlog.register(User)


class DideroUserGroup(BaseModel):
    """
    Group of users that can be added to different models to definw ownership, etc.
    For now, it is used on tasks to share ownership; meaning any of their actions will update the task
    (eg: Tim and Tom are part of group "Didero". if Tom gets his order approved and
    can send it to the supplier, Tim will also see the task and could act on it).
    """

    name = models.CharField()
    team = models.ForeignKey(
        "users.Team", on_delete=models.CASCADE, related_name="user_groups"
    )
    users = models.ManyToManyField("users.User", related_name="user_groups")

    class Meta:
        # Note: users can't be indexed (ManyToManyField limitation)
        indexes = [
            models.Index(fields=["name"]),
            models.Index(fields=["team"]),
            models.Index(fields=["name", "team"]),
        ]
        unique_together = ("name", "team")


class BounceToken(BaseModel):
    """
    Bounce tokens are short-lived temporary tokens appropriate for sending
    in emails which are exchanged for longer lived auth tokens
    """

    user = models.ForeignKey("users.User", on_delete=models.CASCADE)
    token = models.CharField(max_length=32, default=get_random_token, unique=True)
    expires = models.DateTimeField()
    consumed_at = models.DateTimeField(null=True)
    redirect = models.CharField(blank=True, max_length=256)

    @classmethod
    def create(cls, user, expiry_mins=5, redirect=""):
        if not redirect.startswith(("/", settings.APP_URL)):
            redirect = "/"

        # prevent an open redirect vuln here
        allowed_hosts = [domain_from_url(settings.APP_URL)]
        safe_url = url_has_allowed_host_and_scheme(
            url=redirect,
            allowed_hosts=allowed_hosts,
            require_https=not settings.DEBUG,
        )
        if not safe_url:
            redirect = f"{settings.APP_URL}/"
        expiry = timezone.now() + timedelta(minutes=expiry_mins)
        return cls.objects.create(user=user, expires=expiry, redirect=redirect)

    def get_url(self):
        return f"{settings.APP_URL}/auth/bounce/{self.token}"

    def consume(self):
        if not self.is_valid():
            return False

        logger.info("Consuming bounce token", user_id=self.user_id)
        self.consumed_at = timezone.now()
        self.save(update_fields=["consumed_at"])

        # Update user's last_login field when magic link is consumed
        self.user.last_login = timezone.now()
        self.user.save(update_fields=["last_login"])

        try:
            return Token.objects.get(user_id=self.user_id)
        except Token.DoesNotExist:
            return Token.objects.create(user_id=self.user_id)

    def is_valid(self):
        if timezone.now() > self.expires:
            logger.error(
                "Bounce token has expired when consuming: marking as invalid",
                user_id=self.user_id,
            )
            return False

        if self.consumed_at is not None:
            logger.error(
                "Bounce token already consumed when consuming: marking as invalid",
                user_id=self.user_id,
            )
            return False

        return True
