# Generated by Django 4.2.7 on 2025-09-08 20:22

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("users", "0061_alter_user_email"),
        (
            "task_triggers",
            "0002_tasktriggerexecution_tasktriggerobservedmodel_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="TaskTriggerActionRun",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("action_type", models.CharField(max_length=50)),
                (
                    "action_config_snapshot",
                    models.JSO<PERSON>ield(
                        help_text="Action configuration used during execution"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("SUCCESS", "SUCCESS"),
                            ("FAILURE", "FAILURE"),
                            ("SKIPPED", "SKIPPED"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "created_object_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("result_payload", models.JSONField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True, null=True)),
                (
                    "celery_task_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TaskTriggerRun",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                ("success", models.BooleanField()),
                (
                    "target_object_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "input_registry",
                    models.JSONField(help_text="Registry used during evaluation"),
                ),
                (
                    "condition_config_snapshot",
                    models.JSONField(
                        help_text="Snapshot of condition_config at execution time"
                    ),
                ),
                (
                    "fetch_fields_snapshot",
                    models.JSONField(
                        help_text="Snapshot of fetch_fields at execution time"
                    ),
                ),
                ("error_message", models.TextField(blank=True, null=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.AlterUniqueTogether(
            name="tasktriggerexecution",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="tasktriggerexecution",
            name="target_content_type",
        ),
        migrations.RemoveField(
            model_name="tasktriggerexecution",
            name="team",
        ),
        migrations.RemoveField(
            model_name="tasktriggerexecution",
            name="trigger",
        ),
        migrations.AddField(
            model_name="tasktrigger",
            name="archived_at",
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.AddIndex(
            model_name="tasktrigger",
            index=models.Index(
                fields=["team", "is_active", "archived_at"],
                name="task_trigge_team_id_5043d7_idx",
            ),
        ),
        migrations.DeleteModel(
            name="TaskTriggerExecution",
        ),
        migrations.AddField(
            model_name="tasktriggerrun",
            name="target_content_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="contenttypes.contenttype",
            ),
        ),
        migrations.AddField(
            model_name="tasktriggerrun",
            name="team",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="users.team"
            ),
        ),
        migrations.AddField(
            model_name="tasktriggerrun",
            name="trigger",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="runs",
                to="task_triggers.tasktrigger",
            ),
        ),
        migrations.AddField(
            model_name="tasktriggeractionrun",
            name="action",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="runs",
                to="task_triggers.tasktriggeraction",
            ),
        ),
        migrations.AddField(
            model_name="tasktriggeractionrun",
            name="created_object_content_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="contenttypes.contenttype",
            ),
        ),
        migrations.AddField(
            model_name="tasktriggeractionrun",
            name="run",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="action_runs",
                to="task_triggers.tasktriggerrun",
            ),
        ),
        migrations.AddIndex(
            model_name="tasktriggerrun",
            index=models.Index(
                fields=["team", "trigger", "-created_at"],
                name="task_trigge_team_id_173c56_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="tasktriggerrun",
            index=models.Index(
                fields=["target_content_type", "target_object_id", "-created_at"],
                name="task_trigge_target__599c39_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="tasktriggerrun",
            index=models.Index(
                fields=["success", "-created_at"], name="task_trigge_success_5e6350_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tasktriggeractionrun",
            index=models.Index(
                fields=["run", "-created_at"], name="task_trigge_run_id_c0487f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tasktriggeractionrun",
            index=models.Index(
                fields=["status", "-created_at"], name="task_trigge_status_6b9ce9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tasktriggeractionrun",
            index=models.Index(
                fields=["created_object_content_type", "created_object_id"],
                name="task_trigge_created_bc95d4_idx",
            ),
        ),
    ]
