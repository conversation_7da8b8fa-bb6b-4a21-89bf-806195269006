import structlog
from django.apps import AppConfig

logger = structlog.get_logger(__name__)


class TaskTriggersConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "didero.task_triggers"

    def ready(self):
        # Import signal handlers to register post_save hooks on supported models
        try:
            # noqa to avoid linting warnings about unused import; import triggers registration
            from . import signals  # noqa: F401
        except Exception as e:
            # Do not crash app startup; just log the error
            logger.error(
                "task_triggers.signals_import_failed", error=str(e), exc_info=True
            )
