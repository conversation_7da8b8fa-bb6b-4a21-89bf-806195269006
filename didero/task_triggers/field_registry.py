"""
Field Registry for Task Triggers

Defines allowed fields per model that can be used in trigger conditions.
This provides type safety and prevents arbitrary field access.
"""

from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set


class FieldType(Enum):
    """Supported field types for trigger conditions."""

    STRING = "string"
    NUMBER = "number"
    DATE = "date"
    DATETIME = "datetime"
    BOOLEAN = "boolean"
    CURRENCY = "currency"
    CHOICE = "choice"  # For Django choice fields


class ComparisonOperator(Enum):
    """Supported comparison operators."""

    # Universal operators
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    IS_NULL = "is_null"
    IS_NOT_NULL = "is_not_null"

    # Numeric/Date operators
    GREATER_THAN = "greater_than"
    GREATER_THAN_OR_EQUAL = "greater_than_or_equal"
    LESS_THAN = "less_than"
    LESS_THAN_OR_EQUAL = "less_than_or_equal"
    BETWEEN = "between"

    # String operators
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"

    # Date-specific operators
    HOURS_FROM_NOW = (
        "hours_from_now"  # Whole hours from now to the date (negative = past)
    )
    DAYS_FROM_NOW = "days_from_now"  # Whole days from now to the date (negative = past)
    IN_PAST = "in_past"
    IN_FUTURE = "in_future"

    # List operators
    IN = "in"
    NOT_IN = "not_in"


class FieldDefinition:
    """Defines a field that can be used in trigger conditions."""

    def __init__(
        self,
        name: str,
        display_name: str,
        field_type: FieldType,
        allowed_operators: List[ComparisonOperator],
        choices: Optional[List[tuple]] = None,
        custom_getter: Optional[Callable] = None,
        description: Optional[str] = None,
    ):
        self.name = name
        self.display_name = display_name
        self.field_type = field_type
        self.allowed_operators = allowed_operators
        self.choices = choices  # For choice fields
        self.custom_getter = custom_getter  # For computed fields
        self.description = description

    def get_value(self, instance: Any) -> Any:
        """Get the field value from a model instance."""
        if self.custom_getter:
            return self.custom_getter(instance)
        return getattr(instance, self.name, None)

    def to_dict(self) -> Dict:
        """Convert to dictionary for API responses."""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "type": self.field_type.value,
            "operators": [op.value for op in self.allowed_operators],
            "choices": self.choices if self.choices else None,
            "description": self.description,
        }


# Common operator sets for reuse
STRING_OPERATORS = [
    ComparisonOperator.EQUALS,
    ComparisonOperator.NOT_EQUALS,
    ComparisonOperator.CONTAINS,
    ComparisonOperator.NOT_CONTAINS,
    ComparisonOperator.STARTS_WITH,
    ComparisonOperator.ENDS_WITH,
    ComparisonOperator.IS_NULL,
    ComparisonOperator.IS_NOT_NULL,
]

NUMBER_OPERATORS = [
    ComparisonOperator.EQUALS,
    ComparisonOperator.NOT_EQUALS,
    ComparisonOperator.GREATER_THAN,
    ComparisonOperator.GREATER_THAN_OR_EQUAL,
    ComparisonOperator.LESS_THAN,
    ComparisonOperator.LESS_THAN_OR_EQUAL,
    ComparisonOperator.BETWEEN,
    ComparisonOperator.IS_NULL,
    ComparisonOperator.IS_NOT_NULL,
]

DATE_OPERATORS = [
    ComparisonOperator.EQUALS,
    ComparisonOperator.NOT_EQUALS,
    ComparisonOperator.GREATER_THAN,
    ComparisonOperator.LESS_THAN,
    ComparisonOperator.BETWEEN,
    ComparisonOperator.HOURS_FROM_NOW,
    ComparisonOperator.DAYS_FROM_NOW,
    ComparisonOperator.IN_PAST,
    ComparisonOperator.IN_FUTURE,
    ComparisonOperator.IS_NULL,
    ComparisonOperator.IS_NOT_NULL,
]

BOOLEAN_OPERATORS = [
    ComparisonOperator.EQUALS,
    ComparisonOperator.NOT_EQUALS,
]

CHOICE_OPERATORS = [
    ComparisonOperator.EQUALS,
    ComparisonOperator.NOT_EQUALS,
    ComparisonOperator.IN,
    ComparisonOperator.NOT_IN,
    ComparisonOperator.IS_NULL,
    ComparisonOperator.IS_NOT_NULL,
]


# Field Registry - Maps model names to their allowed fields
# Currently supported models: PurchaseOrder, OrderAcknowledgement, Shipment, Supplier
FIELD_REGISTRY: Dict[str, List[FieldDefinition]] = {
    "PurchaseOrder": [
        FieldDefinition(
            name="order_number",
            display_name="PO Number",
            field_type=FieldType.STRING,
            allowed_operators=STRING_OPERATORS,
            description="Purchase order number",
        ),
        FieldDefinition(
            name="order_status",
            display_name="Status",
            field_type=FieldType.CHOICE,
            allowed_operators=CHOICE_OPERATORS,
            choices=[
                ("PENDING", "Pending"),
                ("APPROVED", "Approved"),
                ("AWAITING_SHIPMENT", "Awaiting Shipment"),
                ("SHIPPED", "Shipped"),
                ("COMPLETED", "Completed"),
                ("CANCELLED", "Cancelled"),
            ],
            description="Current status of the purchase order",
        ),
        FieldDefinition(
            name="total_cost",
            display_name="Total Cost",
            field_type=FieldType.CURRENCY,
            allowed_operators=NUMBER_OPERATORS,
            description="Total cost of the purchase order",
        ),
        FieldDefinition(
            name="requested_date",
            display_name="Requested Date",
            field_type=FieldType.DATE,
            allowed_operators=DATE_OPERATORS,
            description="Date when items are requested",
        ),
        FieldDefinition(
            name="placement_time",
            display_name="Placement Time",
            field_type=FieldType.DATETIME,
            allowed_operators=DATE_OPERATORS,
            description="Datetime when the order was placed",
        ),
        FieldDefinition(
            name="payment_terms",
            display_name="Payment Terms",
            field_type=FieldType.STRING,
            allowed_operators=STRING_OPERATORS,
            description="Payment terms for the order",
        ),
        FieldDefinition(
            name="is_urgent",
            display_name="Is Urgent",
            field_type=FieldType.BOOLEAN,
            allowed_operators=BOOLEAN_OPERATORS,
            description="Whether the order is marked as urgent",
        ),
        # Add more PurchaseOrder fields as needed
    ],
    "OrderAcknowledgement": [
        FieldDefinition(
            name="order_number",
            display_name="OA Number",
            field_type=FieldType.STRING,
            allowed_operators=STRING_OPERATORS,
            description="Order acknowledgement number",
        ),
        FieldDefinition(
            name="status",
            display_name="Status",
            field_type=FieldType.CHOICE,
            allowed_operators=CHOICE_OPERATORS,
            choices=[
                ("PENDING", "Pending"),
                ("COMPLETE", "Complete"),
                ("INCOMPLETE", "Incomplete"),
                ("CANCELLED", "Cancelled"),
            ],
            description="Order acknowledgement status",
        ),
        FieldDefinition(
            name="promised_ship_date",
            display_name="Promised Ship Date",
            field_type=FieldType.DATE,
            allowed_operators=DATE_OPERATORS,
            description="Promised shipping date",
        ),
        FieldDefinition(
            name="promised_delivery_date",
            display_name="Promised Delivery Date",
            field_type=FieldType.DATE,
            allowed_operators=DATE_OPERATORS,
            description="Promised delivery date",
        ),
        FieldDefinition(
            name="total_amount",
            display_name="Total Amount",
            field_type=FieldType.CURRENCY,
            allowed_operators=NUMBER_OPERATORS,
            description="Total amount of the order acknowledgement",
        ),
        FieldDefinition(
            name="needs_followup",
            display_name="Needs Follow-up",
            field_type=FieldType.BOOLEAN,
            allowed_operators=BOOLEAN_OPERATORS,
            description="Whether the OA needs follow-up",
        ),
        # Add more OrderAcknowledgement fields as needed
    ],
    "Supplier": [
        FieldDefinition(
            name="name",
            display_name="Supplier Name",
            field_type=FieldType.STRING,
            allowed_operators=STRING_OPERATORS,
            description="Name of the supplier",
        ),
        FieldDefinition(
            name="status",
            display_name="Status",
            field_type=FieldType.CHOICE,
            allowed_operators=CHOICE_OPERATORS,
            choices=[
                ("ACTIVE", "Active"),
                ("INACTIVE", "Inactive"),
                ("PENDING", "Pending"),
            ],
            description="Supplier status",
        ),
        FieldDefinition(
            name="payment_terms",
            display_name="Payment Terms",
            field_type=FieldType.STRING,
            allowed_operators=STRING_OPERATORS,
            description="Default payment terms",
        ),
        FieldDefinition(
            name="lead_time_days",
            display_name="Lead Time (Days)",
            field_type=FieldType.NUMBER,
            allowed_operators=NUMBER_OPERATORS,
            description="Average lead time in days",
        ),
        # Add more Supplier fields as needed
    ],
    "Shipment": [
        FieldDefinition(
            name="tracking_number",
            display_name="Tracking Number",
            field_type=FieldType.STRING,
            allowed_operators=STRING_OPERATORS,
            description="Shipment tracking number",
        ),
        FieldDefinition(
            name="status",
            display_name="Status",
            field_type=FieldType.CHOICE,
            allowed_operators=CHOICE_OPERATORS,
            choices=[
                ("PENDING", "Pending"),
                ("IN_TRANSIT", "In Transit"),
                ("DELIVERED", "Delivered"),
                ("DELAYED", "Delayed"),
                ("CANCELLED", "Cancelled"),
            ],
            description="Shipment status",
        ),
        FieldDefinition(
            name="shipped_date",
            display_name="Shipped Date",
            field_type=FieldType.DATE,
            allowed_operators=DATE_OPERATORS,
            description="Date when shipment was sent",
        ),
        FieldDefinition(
            name="expected_delivery_date",
            display_name="Expected Delivery",
            field_type=FieldType.DATE,
            allowed_operators=DATE_OPERATORS,
            description="Expected delivery date",
        ),
        # Add more Shipment fields as needed
    ],
}


def get_supported_models() -> List[str]:
    """Get list of models that support task triggers."""
    return list(FIELD_REGISTRY.keys())


def get_model_fields(model_name: str) -> List[FieldDefinition]:
    """Get allowed fields for a model."""
    return FIELD_REGISTRY.get(model_name, [])


def get_field_definition(model_name: str, field_name: str) -> Optional[FieldDefinition]:
    """Get a specific field definition."""
    fields = get_model_fields(model_name)
    for field in fields:
        if field.name == field_name:
            return field
    return None


def validate_field_exists(model_name: str, field_name: str) -> bool:
    """Check if a field is allowed for a model."""
    return get_field_definition(model_name, field_name) is not None


def get_allowed_operators(model_name: str, field_name: str) -> List[ComparisonOperator]:
    """Get allowed operators for a field."""
    field_def = get_field_definition(model_name, field_name)
    if field_def:
        return field_def.allowed_operators
    return []


def extract_monitored_fields(condition_config: Dict) -> Set[str]:
    """
    Extract all field names referenced in a condition configuration.
    Recursively walks the condition tree.
    """
    fields = set()

    def _extract(node: Dict):
        if not isinstance(node, dict):
            return

        node_type = node.get("type")

        if node_type == "comparison":
            # Leaf node - extract field name
            field = node.get("field")
            if field:
                fields.add(field)
        elif node_type == "logical":
            # Branch node - recurse into children
            children = node.get("children", [])
            for child in children:
                _extract(child)

    _extract(condition_config)
    return fields
