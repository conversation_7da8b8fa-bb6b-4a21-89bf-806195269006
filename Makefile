deps:
	DJAN<PERSON><PERSON>_SETTINGS_MODULE=didero.settings.common uv sync --dev

django: clean collectstatic update-task-types
	DJ<PERSON><PERSON>O_SETTINGS_MODULE=didero.settings.common uv run python manage.py runserver

public: clean collectstatic
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py runserver 0.0.0.0:8000

# Run celery for all queues. In prod, these different queues will be in different workers. But opening up
# many terminals locally is annoying. Instead, we have one command to just run them all in one terminal.
# If more queues are declared, make sure to add them here, in order to pick them up locally.
celery: clean collectstatic
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run celery -A didero worker --beat -Q default,email_backfills,ai_workflows,bulk_supplier_imports,periodic_tasks,task_email_notifications,opensearch_indexing,nylas_email_notifications --loglevel=info --pool threads

# Run celery worker for only the <PERSON><PERSON><PERSON> email notifications queue
celery-nylas: clean collectstatic
	AWS_PROFILE=didero-dev DJANGO_SETTINGS_MODULE=didero.settings.common uv run celery -A didero worker -Q ai_workflows,nylas_email_notifications --loglevel=info --pool solo

make-queues-staging: clean collectstatic
	DJANGO_SETTINGS_MODULE=didero.settings.staging uv run python manage.py runscript make_queues

make-queues-prod: clean collectstatic
	DJANGO_SETTINGS_MODULE=didero.settings.prod uv run python manage.py runscript make_queues

websocket: clean collectstatic
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run uvicorn didero.asgi:application --reload --port 8000

shell:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py shell

dbshell:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py dbshell

migrate:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py migrate

new-migration:
newmigration:
newmigrations: clean
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py makemigrations
show:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py showmigrations
shownew:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py showmigrations --plan | grep '\[ \]'
collectstatic:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py collectstatic --noinput

unit-tests: clean
	DJANGO_SETTINGS_MODULE=didero.settings.test uv run python manage.py test $(TESTS) --parallel 4 --noinput

integration-tests: clean
	uv run pytest tests/workflows/integration/ -m integration -v

e2e-tests: clean
	DJANGO_SETTINGS_MODULE=didero.settings.test uv run pytest tests/workflows/e2e/ -m e2e -v

tests: unit-tests integration-tests

temporal: clean
	temporal server start-dev

# Create temporal search attribute for PO number (run once for local setup)
create-temporal-search-attribute:
	temporal operator search-attribute create --namespace default --name po_number --type Keyword

# Run Temporal worker listening to all queues (for local development)
temporal-worker: clean
	DJANGO_SETTINGS_MODULE=didero.settings.common TEMPORAL_MAX_WORKERS=20 uv run python manage.py temporal_worker --queue "user_workflows,po_creation_queue,order_ack_queue,shipment_queue,follow_up_queue,invoice_processing_queue,shipping_document_queue"


# Run Temporal workers for specific queues (for testing production-like setup)
temporal-worker-po: clean
	DJANGO_SETTINGS_MODULE=didero.settings.common TEMPORAL_MAX_WORKERS=15 uv run python manage.py temporal_worker --queue po_creation_queue

temporal-worker-oa: clean
	DJANGO_SETTINGS_MODULE=didero.settings.common TEMPORAL_MAX_WORKERS=15 uv run python manage.py temporal_worker --queue order_ack_queue

temporal-worker-shipment: clean
	DJANGO_SETTINGS_MODULE=didero.settings.common TEMPORAL_MAX_WORKERS=20 uv run python manage.py temporal_worker --queue shipment_queue

temporal-worker-follow-up: clean
	DJANGO_SETTINGS_MODULE=didero.settings.common TEMPORAL_MAX_WORKERS=10 uv run python manage.py temporal_worker --queue follow_up_queue

temporal-worker-general: clean
	DJANGO_SETTINGS_MODULE=didero.settings.common TEMPORAL_MAX_WORKERS=10 uv run python manage.py temporal_worker --queue user_workflows

clean:
	export PYTHONDONTWRITEBYTECODE=1
	find . -name '*.pyc' -delete

# Target for checking if AWS SSO session is active and refreshing if needed
check-sso:
	@echo "Checking AWS SSO session..."
	@if ! aws sts get-caller-identity --profile didero-dev > /dev/null 2>&1; then \
		echo "AWS SSO session not active or expired. Logging in..."; \
		aws sso login --profile didero-dev; \
	else \
		echo "AWS SSO session is active."; \
	fi

# Target for starting all Docker services with AWS SSO credentials
docker-dev-start: check-sso
	@echo "Starting Docker development environment with AWS SSO credentials..."
	@eval $$(aws configure export-credentials --profile didero-dev --format env) && \
	docker-compose --env-file ./.env.docker up -d
	@echo "Docker development environment started successfully."

# Stop all Docker services
docker-dev-stop:
	@echo "Stopping Docker development environment..."
	@docker-compose down
	@echo "Docker development environment stopped."

# Restart Docker services with fresh credentials
docker-dev-restart: docker-dev-stop docker-dev-start

# Update the task types v2s and task actions in the database
update-task-types:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py update_task_types

update_task_types:
	DJANGO_SETTINGS_MODULE=didero.settings.common uv run python manage.py update_task_types
