#!/bin/bash

# <PERSON>ript to run e2e tests with proper environment setup
# This ensures clean test environment and proper isolation

set -e  # Exit on error

echo "🧪 Running E2E Tests..."

# Set test environment
export DJANGO_SETTINGS_MODULE=didero.settings.test
export PYTHONPATH="${PYTHONPATH:-$(pwd)}"

# Check if Redis is available
if command -v redis-cli &> /dev/null; then
    # Clear Redis test database (DB 15) if Redis is available
    echo "📦 Clearing Redis test database..."
    redis-cli -n 15 FLUSHDB >/dev/null 2>&1 || true
else
    echo "⚠️  Redis not found, skipping Redis cleanup"
fi

# Run e2e tests with pytest and coverage
# Django's test framework will automatically create and migrate the test database
echo "🚀 Starting e2e test suite with coverage..."
echo "📝 Django will create test database automatically..."
uv run coverage run --source=didero -m pytest tests/workflows/e2e \
    --verbosity=2 \
    --tb=short \
    --color=yes \
    "$@"  # Pass any additional arguments to pytest

# Capture test exit code before generating reports
TEST_EXIT_CODE=$?

# Generate coverage reports (even if tests failed, for debugging)
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "📊 Generating coverage reports..."
    uv run coverage report --show-missing
    uv run coverage html
else
    echo "⚠️  Tests failed, generating coverage reports anyway for debugging..."
    uv run coverage report --show-missing || true
    uv run coverage html || true
fi

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ E2E tests passed successfully!"
else
    echo "❌ E2E tests failed with exit code $TEST_EXIT_CODE"
fi

# Exit with test status
exit $TEST_EXIT_CODE