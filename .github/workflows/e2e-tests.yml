name: E2E Tests

on:
  pull_request:
    branches: [staging, main]
    paths:
      - 'didero/**'
      - 'tests/**'
      - 'pyproject.toml'
      - 'uv.lock'
      - '.github/workflows/e2e-tests.yml'
  schedule:
    # Run nightly at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:  # Allow manual trigger

jobs:
  e2e-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 60
    
    services:
      postgres:
        image: postgres:13-alpine
        env:
          POSTGRES_USER: didero
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_didero_e2e
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: |
            **/uv.lock
            **/pyproject.toml

      - name: Install dependencies
        run: |
          uv sync --frozen

      - name: Run E2E tests with coverage
        id: run-tests
        env:
          CI: true
          DJANGO_SETTINGS_MODULE: didero.settings.test
          POSTGRES_HOST: localhost
          POSTGRES_PASSWORD: postgres
          # Basic required env vars (encryption is disabled in tests)
          DJANGO_SECRET_KEY: test-secret-key-for-ci
          GOOGLE_AUTH_CRED: '{"web": {}}'
          # Dummy encryption keys (not used due to TESTING=True, but prevents import errors)
          KEY_USER_PII: dummy-key-not-used-in-tests-32b!!
          # Use GitHub Secrets for real API keys (if tests actually need them)
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          LANGFUSE_PUBLIC_KEY: ${{ secrets.LANGFUSE_PUBLIC_KEY }}
          LANGFUSE_SECRET_KEY: ${{ secrets.LANGFUSE_SECRET_KEY }}
          LANGFUSE_HOST: ${{ secrets.LANGFUSE_HOST }}
        run: |
          # Show test discovery for debugging
          echo "🔍 Discovering tests..."
          uv run pytest tests/workflows/e2e --collect-only -q
          echo "📊 Test discovery complete"
          echo ""
          
          # Run tests with coverage, capturing exit code but not failing the step
          set -o pipefail
          set +e
          uv run coverage run --source=didero -m pytest tests/workflows/e2e \
            -v \
            --junitxml=pytest-e2e.xml | tee pytest-e2e.log
          
          # Store test exit code for later
          echo ${PIPESTATUS[0]} > .test_exit_code
          
          # Always exit successfully so coverage generation can run
          exit 0

      - name: Generate coverage reports
        if: always()
        run: |
          echo "📊 Generating coverage reports..."
          uv run coverage xml -i || echo "Warning: Failed to generate coverage.xml"
          uv run coverage html -i || echo "Warning: Failed to generate HTML coverage"
          
          # Generate text report for summary
          echo "📊 Coverage summary:"
          uv run coverage report -i || echo "No coverage data available"
          
          # Verify coverage files were created
          echo "📂 Checking for coverage files..."
          if [ -f "coverage.xml" ]; then
            echo "✅ coverage.xml created successfully"
          else
            echo "❌ coverage.xml was not created"
            echo "📂 Current directory contents:"
            ls -la | grep -E "(coverage|\.coverage)" || true
          fi
          
          if [ -d "htmlcov" ]; then
            echo "✅ htmlcov/ directory created successfully"
          else
            echo "❌ htmlcov/ directory was not created"
          fi

      # Generate test summary using Python (the working approach)
      - name: Generate Test Summary
        if: always()
        run: |
          python3 << 'EOF'
          import os, xml.etree.ElementTree as ET, json, sys
          from datetime import datetime
          
          # Parse JUnit for test counts
          tests, failures, errors, skipped = 0, 0, 0, 0
          test_details = {"passed": [], "failed": [], "skipped": []}
          
          junit = "pytest-e2e.xml"
          if os.path.exists(junit):
              tree = ET.parse(junit)
              root = tree.getroot()
              
              # Handle both <testsuite> and <testsuites> formats
              testsuites = root.findall("testsuite") if root.tag == "testsuites" else [root]
              
              for suite in testsuites:
                  tests += int(suite.attrib.get("tests", 0))
                  failures += int(suite.attrib.get("failures", 0))
                  errors += int(suite.attrib.get("errors", 0))
                  skipped += int(suite.attrib.get("skipped", 0))
                  
                  # Extract individual test details
                  for testcase in suite.findall("testcase"):
                      classname = testcase.get("classname", "")
                      name = testcase.get("name", "")
                      test_name = f"{classname}::{name}"
                      
                      if testcase.find("skipped") is not None:
                          skip_elem = testcase.find("skipped")
                          reason = skip_elem.get("message", "") if skip_elem is not None else ""
                          test_details["skipped"].append(f"{test_name} - {reason}" if reason else test_name)
                      elif testcase.find("failure") is not None or testcase.find("error") is not None:
                          test_details["failed"].append(test_name)
                      else:
                          test_details["passed"].append(test_name)
          
          passed = tests - failures - errors - skipped
          
          # Parse coverage
          line_pct = 0.0
          cov_xml = "coverage.xml"
          if os.path.exists(cov_xml):
              print(f"📊 Found coverage.xml file")
              c = ET.parse(cov_xml).getroot()
              line_rate = c.get("line-rate", "0")
              print(f"📊 Raw line-rate: {line_rate}")
              line_pct = float(line_rate) * 100
              print(f"📊 Calculated coverage: {line_pct:.1f}% lines")
          else:
              print(f"❌ Coverage file not found: {cov_xml}")
              # List files in current directory for debugging
              print("📂 Files in current directory:")
              for f in os.listdir("."):
                  if "coverage" in f.lower() or f.endswith(".xml"):
                      print(f"  - {f}")
          
          status_icon = "✅" if failures == 0 and errors == 0 else "❌"
          status_text = "Success" if failures == 0 and errors == 0 else "Failed"
          
          # Build GitHub markdown summary
          md = []
          md.append("## 📊 E2E Test Results Summary")
          md.append("")
          md.append(f"**Status:** {status_icon} {status_text}")
          md.append("")
          
          # Test statistics table
          md.append("### 📈 Test Statistics")
          md.append("| Metric | Count |")
          md.append("|--------|-------|")
          md.append(f"| ✅ Passed | {passed} |")
          md.append(f"| ❌ Failed | {failures} |")
          md.append(f"| 🔥 Errors | {errors} |")
          md.append(f"| ⏭️ Skipped | {skipped} |")
          md.append(f"| 📝 Total | {tests} |")
          md.append(f"| 📊 Line Coverage | {line_pct:.1f}% |")
          md.append("")
          
          # Test details with collapsible sections
          md.append("### 🧪 Test Details")
          md.append("")
          
          if test_details["passed"]:
              md.append("<details>")
              md.append(f"<summary>✅ Passed Tests ({len(test_details['passed'])})</summary>")
              md.append("")
              md.append("```")
              for test in test_details["passed"]:
                  md.append(test)
              md.append("```")
              md.append("</details>")
              md.append("")
          
          if test_details["skipped"]:
              md.append("<details>")
              md.append(f"<summary>⏭️ Skipped Tests ({len(test_details['skipped'])})</summary>")
              md.append("")
              md.append("```")
              for test in test_details["skipped"]:
                  md.append(test)
              md.append("```")
              md.append("</details>")
              md.append("")
          
          if test_details["failed"]:
              md.append("<details>")
              md.append(f"<summary>❌ Failed Tests ({len(test_details['failed'])})</summary>")
              md.append("")
              md.append("```")
              for test in test_details["failed"]:
                  md.append(test)
              md.append("```")
              md.append("</details>")
              md.append("")
          
          # Execution details
          md.append("### ⏱️ Execution Details")
          md.append(f"- **Run Time**: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
          md.append(f"- **Triggered By**: ${{ github.event_name }}")
          md.append(f"- **Branch**: ${{ github.ref_name }}")
          md.append(f"- **Commit**: ${{ github.sha }}")
          md.append(f"- **Run**: [${{ github.run_id }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})")
          md.append("")
          
          # Artifacts
          md.append("### 📎 Artifacts")
          md.append("Download the following artifacts from this workflow run:")
          md.append("- 📈 HTML Coverage Report (htmlcov/)")
          md.append("- 📊 Coverage XML (coverage.xml)")
          md.append("- 📋 Test Log (pytest-e2e.log)")
          
          summary_md = "\n".join(md)
          
          # Write summary to file
          with open("summary.md", "w") as f:
              f.write(summary_md)
          
          # Also create JSON for potential Slack integration
          payload = {
              "status": "success" if failures == 0 and errors == 0 else "failure",
              "tests": tests,
              "passed": passed,
              "failures": failures,
              "errors": errors,
              "skipped": skipped,
              "line_coverage": round(line_pct, 1),
              "workflow": "${{ github.workflow }}",
              "branch": "${{ github.ref_name }}",
              "run_url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          }
          with open("summary.json", "w") as f:
              json.dump(payload, f, indent=2)
          
          print(f"📊 Tests: {tests}, Passed: {passed}, Failed: {failures}, Skipped: {skipped}")
          print(f"📈 Coverage: {line_pct:.1f}% lines")
          EOF
          
          # Add to GitHub summary
          cat summary.md >> $GITHUB_STEP_SUMMARY

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-coverage-and-logs
          path: |
            .coverage
            coverage.xml
            htmlcov/
            pytest-e2e.xml
            pytest-e2e.log
          retention-days: 7

      - name: Send Slack notification (nightly only)
        if: always() && github.event_name == 'schedule'
        run: |
          # Read test results from summary.json
          if [ -f "summary.json" ]; then
            STATUS=$(cat summary.json | python3 -c "import sys, json; print(json.load(sys.stdin)['status'])")
            TESTS=$(cat summary.json | python3 -c "import sys, json; print(json.load(sys.stdin)['tests'])")
            PASSED=$(cat summary.json | python3 -c "import sys, json; print(json.load(sys.stdin)['passed'])")
            FAILURES=$(cat summary.json | python3 -c "import sys, json; print(json.load(sys.stdin)['failures'])")
            ERRORS=$(cat summary.json | python3 -c "import sys, json; print(json.load(sys.stdin)['errors'])")
            COVERAGE=$(cat summary.json | python3 -c "import sys, json; print(json.load(sys.stdin)['line_coverage'])")
            RUN_URL=$(cat summary.json | python3 -c "import sys, json; print(json.load(sys.stdin)['run_url'])")
            
            # Choose emoji and color based on status
            if [ "$STATUS" = "success" ]; then
              EMOJI="✅"
              COLOR="good"
              STATUS_TEXT="Success"
            else
              EMOJI="❌" 
              COLOR="danger"
              STATUS_TEXT="Failed"
            fi
            
            # Create Slack payload
            cat > slack_payload.json << EOF
          {
            "attachments": [
              {
                "color": "$COLOR",
                "title": "$EMOJI Nightly E2E Test Results",
                "title_link": "$RUN_URL",
                "fields": [
                  {
                    "title": "Status",
                    "value": "$STATUS_TEXT",
                    "short": true
                  },
                  {
                    "title": "Coverage", 
                    "value": "$COVERAGE%",
                    "short": true
                  },
                  {
                    "title": "Tests",
                    "value": "$TESTS total",
                    "short": true
                  },
                  {
                    "title": "Results",
                    "value": "✅ $PASSED passed, ❌ $FAILURES failed, 🔥 $ERRORS errors",
                    "short": true
                  }
                ],
                "footer": "Didero API E2E Tests",
                "ts": $(date +%s)
              }
            ]
          }
          EOF
            
            # Send to Slack
            curl -X POST -H 'Content-type: application/json' \
              --data @slack_payload.json \
              "${{ secrets.SLACK_WEBHOOK_URL }}" \
              && echo "✅ Slack notification sent successfully" \
              || echo "❌ Failed to send Slack notification"
          else
            echo "No summary.json found, skipping Slack notification"
          fi

      - name: Check test results and fail if needed
        if: always()
        run: |
          if [ -f .test_exit_code ]; then
            TEST_EXIT_CODE=$(cat .test_exit_code)
            if [ "$TEST_EXIT_CODE" -ne 0 ]; then
              echo "❌ Tests failed with exit code: $TEST_EXIT_CODE"
              exit $TEST_EXIT_CODE
            else
              echo "✅ All tests passed"
            fi
          else
            echo "⚠️ No test exit code file found"
            exit 1
          fi