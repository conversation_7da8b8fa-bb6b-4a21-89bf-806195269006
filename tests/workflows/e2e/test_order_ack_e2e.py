"""
E2E tests for Order Acknowledgement workflow.
Tests complete order acknowledgement processing flow from email to OA creation.
"""

import logging
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

import pytest
from asgiref.sync import sync_to_async
from temporalio.worker import Worker

from didero.workflows.core_workflows.order_ack.workflow import (
    OrderAcknowledgementWorkflow,
)
from tests.workflows.e2e.utils import handle_workflow_failure, log_workflow_success

logger = logging.getLogger(__name__)


@pytest.mark.e2e
@pytest.mark.django_db(transaction=True)
class TestOrderAckE2E:
    """End-to-end tests for Order Acknowledgement workflow using real activities."""

    @pytest.fixture
    def sample_oa_email(self, fixture_path):
        """Load sample order acknowledgement email fixture."""
        import json

        with open(fixture_path / "sample_oa_email.json") as f:
            return json.load(f)

    @pytest.fixture
    def sample_oa_email_existing_po(self, fixture_path):
        """Load sample order acknowledgement email fixture for existing PO test."""
        import json

        with open(fixture_path / "sample_oa_email_existing_po.json") as f:
            return json.load(f)

    async def run_order_ack_workflow(
        self,
        temporal_env,
        test_data_generator,
        email_data=None,
        team=None,
        workflow_config=None,
        existing_po=None,
    ):
        """
        Execute Order Acknowledgement workflow with real activities.

        Args:
            temporal_env: Temporal test environment
            test_data_generator: Test data generator instance
            email_data: Optional email data for workflow input
            team: Optional existing team (creates new if None)
            workflow_config: Optional existing workflow config
            existing_po: Optional existing purchase order

        Returns:
            Workflow execution result
        """

        if not team:
            scenario = await test_data_generator.create_complete_oa_scenario(
                team_name="E2E Order Ack Test Team"
            )
            team = scenario["team"]
            workflow = scenario["workflow"]
        else:
            workflow = workflow_config

        po = existing_po
        if not po:
            po = await test_data_generator.create_purchase_order(
                team=team,
                supplier=scenario["supplier"] if not team else None,
                user=scenario["user"] if not team else None,
                po_number="PO-OA-001",
                status="issued",
            )

        email = None
        if email_data:
            from didero.suppliers.models import Supplier

            email_body = email_data.get("body", "")
            supplier_name = None
            if "Test Supplier Inc" in email_body:
                supplier_name = "Test Supplier Inc"

            if supplier_name:
                existing_supplier = await sync_to_async(
                    lambda: Supplier.objects.filter(
                        team=team, name=supplier_name
                    ).first()
                )()

                if existing_supplier:
                    supplier = existing_supplier
                else:
                    supplier = await test_data_generator.create_supplier(
                        team=team,
                        name=supplier_name,
                        email=email_data.get("sender", "<EMAIL>"),
                    )
            else:
                supplier = po.supplier if po else scenario["supplier"]

            email = await test_data_generator.create_email_with_attachment(
                team=team,
                subject=email_data["subject"],
                body=email_data["body"],
                sender=email_data.get("sender", "<EMAIL>"),
                supplier=supplier,
            )

        params = {
            "email_id": str(email.id) if email else None,
            "team_id": str(team.id),
            "workflow_id": str(workflow.id) if workflow else None,
        }

        from didero.workflows.core_workflows.activities import (
            get_task_assignment_user_id,
            load_workflow_config,
        )
        from didero.workflows.core_workflows.order_ack.activities import (
            save_oa_activity,
        )
        from didero.workflows.shared_activities.document_linking import (
            link_email_workflow_documents,
        )
        from didero.workflows.shared_activities.document_matching import (
            match_documents_activity,
        )
        from didero.workflows.shared_activities.purchase_order_operations import (
            get_purchase_order_details_activity,
        )
        from didero.workflows.shared_activities.supplier_document_operations import (
            extract_supplier_document,
            resolve_purchase_order_for_document,
        )

        async with Worker(
            temporal_env.client,
            task_queue="e2e-order-ack",
            workflows=[OrderAcknowledgementWorkflow],
            activities=[
                load_workflow_config,
                extract_supplier_document,
                resolve_purchase_order_for_document,
                get_purchase_order_details_activity,
                match_documents_activity,
                save_oa_activity,
                link_email_workflow_documents,
                get_task_assignment_user_id,
            ],
            activity_executor=ThreadPoolExecutor(max_workers=2),
        ):
            handle = await temporal_env.client.start_workflow(
                OrderAcknowledgementWorkflow.run,
                args=[str(workflow.id), params],
                id=f"e2e-order-ack-{datetime.now().isoformat()}",
                task_queue="e2e-order-ack",
                run_timeout=timedelta(minutes=3),
            )

            try:
                result = await handle.result()
                return result
            except Exception:
                await handle_workflow_failure(handle, "Order Acknowledgement")
                raise

    @pytest.mark.asyncio
    async def test_order_ack_happy_path(
        self, temporal_env, test_data_generator, sample_oa_email
    ):
        """Test basic order acknowledgement workflow execution."""

        # Run workflow with sample email
        result = await self.run_order_ack_workflow(
            temporal_env,
            test_data_generator,
            email_data=sample_oa_email,
        )

        # Verify workflow succeeded
        assert result["success"] is True
        assert result["details"]["oa_id"] is not None

        log_workflow_success("Order Acknowledgement", result["details"])

    @pytest.mark.asyncio
    async def test_order_ack_with_existing_po(
        self, temporal_env, test_data_generator, sample_oa_email_existing_po
    ):
        """Test order acknowledgement workflow with existing purchase order."""

        # Create scenario and PO first
        scenario = await test_data_generator.create_complete_oa_scenario(
            team_name="E2E OA Existing PO Test"
        )

        existing_po = await test_data_generator.create_purchase_order(
            team=scenario["team"],
            supplier=scenario["supplier"],
            user=scenario["user"],
            po_number="PO-EXISTING-OA-001",
            status="issued",
        )

        # Run workflow with existing PO
        result = await self.run_order_ack_workflow(
            temporal_env,
            test_data_generator,
            email_data=sample_oa_email_existing_po,
            team=scenario["team"],
            workflow_config=scenario["workflow"],
            existing_po=existing_po,
        )

        # Verify workflow succeeded
        assert result["success"] is True
        assert result["details"]["oa_id"] is not None

        log_workflow_success(
            "Order Acknowledgement with existing PO", result["details"]
        )
