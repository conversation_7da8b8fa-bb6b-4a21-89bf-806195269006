"""
E2E tests for Shipping Document Processing workflow.
Tests complete shipping document processing flow from email to document creation.
"""

import logging
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

import pytest
from asgiref.sync import sync_to_async
from temporalio.worker import Worker

from didero.workflows.core_workflows.shipping_document_processing.workflow import (
    ShippingDocumentProcessingWorkflow,
)
from tests.workflows.e2e.utils import handle_workflow_failure, log_workflow_success

logger = logging.getLogger(__name__)


@pytest.mark.e2e
@pytest.mark.django_db(transaction=True)
class TestShippingDocProcessingE2E:
    """End-to-end tests for Shipping Document Processing workflow using real activities."""

    @pytest.fixture
    def sample_shipping_doc_email(self, fixture_path):
        """Load sample shipping document email fixture."""
        import json

        with open(fixture_path / "sample_shipping_doc_email.json") as f:
            return json.load(f)

    @pytest.fixture
    def sample_shipping_doc_email_existing_po(self, fixture_path):
        """Load sample shipping document email fixture for existing PO test."""
        import json

        with open(fixture_path / "sample_shipping_doc_email_existing_po.json") as f:
            return json.load(f)

    async def run_shipping_doc_workflow(
        self,
        temporal_env,
        test_data_generator,
        email_data=None,
        team=None,
        workflow_config=None,
        existing_po=None,
    ):
        """
        Execute Shipping Document Processing workflow with real activities.

        Args:
            temporal_env: Temporal test environment
            test_data_generator: Test data generator instance
            email_data: Optional email data for workflow input
            team: Optional existing team (creates new if None)
            workflow_config: Optional existing workflow config
            existing_po: Optional existing purchase order

        Returns:
            Workflow execution result
        """

        # Create test scenario if not provided
        if not team:
            scenario = await test_data_generator.create_complete_shipping_doc_scenario(
                team_name="E2E Shipping Doc Test Team"
            )
            team = scenario["team"]
            workflow = scenario["workflow"]
        else:
            workflow = workflow_config

        # Create or use existing PO for the shipping document to reference
        po = existing_po
        if not po:
            # Create a PO that the shipping document can reference
            po = await test_data_generator.create_purchase_order(
                team=team,
                supplier=scenario["supplier"] if not team else None,
                user=scenario["user"] if not team else None,
                po_number="PO-SHIP-DOC-001",  # This should match the email
                status="issued",
            )

        # Create email if data provided
        email = None
        if email_data:
            # Create or get supplier with matching name
            from didero.suppliers.models import Supplier

            # Extract supplier name from email if present
            email_body = email_data.get("body", "")
            supplier_name = None
            if "Test Supplier Inc" in email_body:
                supplier_name = "Test Supplier Inc"

            # Create or get supplier with matching name
            if supplier_name:
                existing_supplier = await sync_to_async(
                    lambda: Supplier.objects.filter(
                        team=team, name=supplier_name
                    ).first()
                )()

                if existing_supplier:
                    supplier = existing_supplier
                else:
                    supplier = await test_data_generator.create_supplier(
                        team=team,
                        name=supplier_name,
                        email=email_data.get("sender", "<EMAIL>"),
                    )
            else:
                # Use existing supplier from scenario or PO
                supplier = po.supplier if po else scenario["supplier"]

            email = await test_data_generator.create_email_with_attachment(
                team=team,
                subject=email_data["subject"],
                body=email_data["body"],
                sender=email_data.get("sender", "<EMAIL>"),
                supplier=supplier,
            )

        # Set up workflow parameters (similar to other document workflows)
        params = {
            "email_id": str(email.id) if email else None,
            "team_id": str(team.id),
            "workflow_id": str(workflow.id) if workflow else None,
        }

        # Import ALL required activities for shipping document processing workflow
        from didero.workflows.core_workflows.activities import (
            load_workflow_config,
        )
        from didero.workflows.shared_activities.document_linking import (
            link_email_workflow_documents,
        )
        from didero.workflows.shared_activities.document_matching import (
            create_document_match_review_task_activity,
            match_documents_activity,
        )
        from didero.workflows.shared_activities.document_retrieval import (
            retrieve_document_activity,
        )
        from didero.workflows.shared_activities.shipping_document_operations import (
            extract_shipping_document_details,
            store_shipping_document_from_email_activity,
        )

        # Start worker with ALL activities
        async with Worker(
            temporal_env.client,
            task_queue="e2e-shipping-doc",
            workflows=[ShippingDocumentProcessingWorkflow],
            activities=[
                # Configuration (always needed first)
                load_workflow_config,
                # Document retrieval and extraction
                retrieve_document_activity,
                extract_shipping_document_details,
                # Document storage
                store_shipping_document_from_email_activity,
                # Email and document linking
                link_email_workflow_documents,
                # Document comparison (if enabled)
                match_documents_activity,
                # Task creation for review
                create_document_match_review_task_activity,
            ],
            activity_executor=ThreadPoolExecutor(max_workers=2),
        ):
            handle = await temporal_env.client.start_workflow(
                ShippingDocumentProcessingWorkflow.run,
                args=[str(workflow.id), params],
                id=f"e2e-shipping-doc-{datetime.now().isoformat()}",
                task_queue="e2e-shipping-doc",
                run_timeout=timedelta(minutes=3),
            )

            try:
                result = await handle.result()
                return result
            except Exception:
                await handle_workflow_failure(handle, "Shipping Document Processing")
                raise

    @pytest.mark.asyncio
    async def test_shipping_doc_happy_path(
        self, temporal_env, test_data_generator, sample_shipping_doc_email
    ):
        """Test successful shipping document processing workflow execution."""

        # Run workflow with sample email
        result = await self.run_shipping_doc_workflow(
            temporal_env,
            test_data_generator,
            email_data=sample_shipping_doc_email,
        )

        # Verify workflow succeeded
        assert result["success"] is True
        assert result["shipping_document_id"] is not None
        assert result["status"] == "completed"

        log_workflow_success(
            "Shipping Document Processing",
            {"shipping_document_id": result["shipping_document_id"]},
        )
