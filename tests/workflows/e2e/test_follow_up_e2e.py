"""
E2E tests for Follow Up workflow.
Tests follow-up activities for purchase orders based on team configuration.
"""

import logging
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

import pytest
from temporalio.worker import Worker

from didero.workflows.core_workflows.follow_up.workflow import FollowUpWorkflow
from tests.workflows.e2e.utils import log_workflow_success

logger = logging.getLogger(__name__)


@pytest.mark.e2e
@pytest.mark.django_db(transaction=True)
class TestFollowUpE2E:
    """End-to-end tests for Follow Up workflow using real activities."""

    async def run_follow_up_workflow(
        self,
        temporal_env,
        test_data_generator,
        team=None,
        workflow_config=None,
        purchase_order=None,
        email_object_id=None,
    ):
        """Helper to run Follow Up workflow with real activities."""

        # Create test scenario if not provided
        if not team:
            scenario = await test_data_generator.create_complete_po_scenario(
                team_name="E2E Follow Up Test Team"
            )
            team = scenario["team"]
            workflow = scenario["workflow"]

            # Configure team follow-up settings with ULTRA-SHORT wait times to compensate for 3600x multiplier
            from asgiref.sync import sync_to_async

            from didero.users.models import TeamSetting, TeamSettingEnums

            # CRITICAL FIX: Since workflow has HOURS_TO_SECONDS_MULTIPLIER = 3600,
            # we need to use FRACTIONAL hours to get reasonable test times
            # 0.001 hours * 3600 = 3.6 seconds actual wait time
            fast_global_config = {
                "workflow_enabled": True,
                "business_hours_only": False,  # Don't wait for business hours in testing
                "timezone": "America/New_York",
            }

            # Since Pydantic expects integers, we'll use 0 hours to minimize wait time
            # The workflow should handle 0 as "immediate" or minimal wait
            fast_oa_config = {
                "enabled": True,
                "initial_wait_hours": 0,  # 0 * 3600 = 0 seconds wait
                "repeat_every_hours": 0,  # 0 * 3600 = 0 seconds wait
                "max_attempts_before_task": 1,  # Only 1 attempt - conditions already met
            }

            fast_shipment_config = {
                "enabled": False,  # Disable - requires AWAITING_SHIPMENT status, not ISSUED
                "time_based_wait_hours": 0,
                "time_based_repeat_hours": 0,
                "date_based_followup_strategy": "balanced",
                "max_attempts_before_task": 1,
            }

            fast_delivery_config = {
                "enabled": True,
                "wait_hours": 0,
                "repeat_hours": 0,
                "max_attempts_before_task": 1,
            }

            # Update team settings

            await sync_to_async(TeamSetting.objects.update_or_create)(
                name=TeamSettingEnums.TEAM_FOLLOWUP_GLOBAL_CONFIG.value,
                team=team,
                user=None,
                defaults={"value": fast_global_config},
            )

            await sync_to_async(TeamSetting.objects.update_or_create)(
                name=TeamSettingEnums.TEAM_FOLLOWUP_OA_CONFIG.value,
                team=team,
                user=None,
                defaults={"value": fast_oa_config},
            )

            await sync_to_async(TeamSetting.objects.update_or_create)(
                name=TeamSettingEnums.TEAM_FOLLOWUP_SHIPMENT_CONFIG.value,
                team=team,
                user=None,
                defaults={"value": fast_shipment_config},
            )

            await sync_to_async(TeamSetting.objects.update_or_create)(
                name=TeamSettingEnums.TEAM_FOLLOWUP_DELIVERY_CONFIG.value,
                team=team,
                user=None,
                defaults={"value": fast_delivery_config},
            )

        else:
            workflow = workflow_config

        # Create or use existing PO
        po = purchase_order
        if not po:
            # Create items for the PO
            items = (
                scenario.get("items", [])
                if not team
                else await test_data_generator.create_items(team, count=2)
            )

            # Create a PO for follow-up (must be in "issued" status for follow-up workflow)
            po = await test_data_generator.create_purchase_order_with_items(
                team=team,
                supplier=scenario["supplier"] if not team else None,
                user=scenario["user"] if not team else None,
                po_number="PO-FU-001",
                status="issued",  # Critical: Follow-up workflow requires ISSUED status
                items=[
                    {"item": items[0], "quantity": 5, "unit_price": "10.00"},
                    {"item": items[1], "quantity": 10, "unit_price": "20.00"},
                ],
            )

            # Create an Order Acknowledgement for the PO so follow-up conditions are met immediately
            from asgiref.sync import sync_to_async

            from didero.orders.models import OrderAcknowledgement

            # Create OA with ship dates to satisfy BOTH "oa" and "ship_dates" conditions
            oa = await sync_to_async(OrderAcknowledgement.objects.create)(
                purchase_order=po,
                order_number=f"OA-{po.po_number}",
            )

            # Create OA items with ship dates to pass ship_dates condition check
            from datetime import date

            from didero.orders.models import OrderAcknowledgementItem

            po_items = await sync_to_async(
                lambda: list(po.items.select_related("item").all())
            )()

            for po_item in po_items:
                await sync_to_async(
                    OrderAcknowledgementItem.objects.create
                )(
                    order_acknowledgement=oa,
                    order_item=po_item,  # Correct field name
                    item=po_item.item,
                    item_number=po_item.item.item_number,
                    quantity=po_item.quantity,
                    unit_price=po_item.price,
                    promised_ship_date=date.today(),  # Critical: Ship date to pass condition
                    promised_delivery_date=date.today(),
                )

            # Verify OA exists and ship dates are complete
            oa_exists = await sync_to_async(
                OrderAcknowledgement.objects.check_oa_exists_for_po
            )(str(po.id))
            ship_dates_complete = await sync_to_async(
                OrderAcknowledgement.objects.check_oa_ship_dates_complete
            )(str(po.id))

        # Set up workflow parameters
        params = {
            "purchase_order_id": str(po.id),
            "email_object_id": str(email_object_id) if email_object_id else "",
        }

        # Import ALL required activities for follow up workflow
        from didero.workflows.core_workflows.follow_up.activities import (
            attempt_follow_up_activity,
            calculate_business_hours_delay_activity,
            oa_follow_up_activity,
            ship_date_follow_up_activity,
            shipment_follow_up_activity,
        )
        from didero.workflows.shared_activities.follow_up_data import (
            get_followup_config_activity,
        )
        from didero.workflows.shared_activities.purchase_order_operations import (
            get_purchase_order_activity,
        )

        # Start worker with ALL activities
        activities = [
            # Follow-up specific activities
            attempt_follow_up_activity,
            calculate_business_hours_delay_activity,
            oa_follow_up_activity,
            ship_date_follow_up_activity,
            shipment_follow_up_activity,
            # Data retrieval activities
            get_followup_config_activity,
            get_purchase_order_activity,
        ]

        async with Worker(
            temporal_env.client,
            task_queue="e2e-follow-up",
            workflows=[FollowUpWorkflow],
            activities=activities,
            activity_executor=ThreadPoolExecutor(max_workers=10),
        ):
            # Execute the workflow
            handle = await temporal_env.client.start_workflow(
                FollowUpWorkflow.run,
                args=[str(workflow.id), params],  # workflow_id, params
                id=f"e2e-follow-up-{datetime.now().isoformat()}",
                task_queue="e2e-follow-up",
                run_timeout=timedelta(
                    seconds=45
                ),  # Increased timeout for complete OA with ship dates
            )

            # Get the result
            result = await handle.result()
            return result

    @pytest.mark.asyncio
    async def test_follow_up_workflow_happy_path(
        self, temporal_env, test_data_generator
    ):
        """Test basic follow up workflow execution."""

        # Run workflow
        result = await self.run_follow_up_workflow(
            temporal_env,
            test_data_generator,
        )

        # Verify workflow succeeded

        # Accept success=True for completed activities
        assert result.success is True
        # Status should be "completed" when all enabled activities succeed
        assert str(result.status) in [
            "completed",
            "partial_completion",
            "FollowUpStatus.COMPLETED",
            "FollowUpStatus.PARTIAL_COMPLETION",
        ]

        log_workflow_success("Follow Up", {"status": result.status})
