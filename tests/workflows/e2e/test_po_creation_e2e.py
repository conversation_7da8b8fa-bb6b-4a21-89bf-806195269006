"""
End-to-end tests for PO Creation workflow.

These tests use REAL activities and REAL document processing to validate
the complete PO creation flow from email/document to database.
"""

import json
import logging
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from decimal import Decimal

import pytest
from asgiref.sync import sync_to_async
from temporalio.worker import Worker

# Import models for assertions
from didero.orders.models import PurchaseOrder
from didero.tasks.models import Task

# Import all real activities used by the workflow
from didero.workflows.core.nodes.purchase_orders.po_creation.activities import (
    create_po,
    create_po_confirmation_task,
    extract_po_details,
    extract_po_from_erp,
)
from didero.workflows.core_workflows.activities import load_workflow_config

# Import the actual workflow and activities
from didero.workflows.core_workflows.po_creation.workflow import (
    POCreationParams,
    POCreationWorkflow,
)
from didero.workflows.shared_activities.document_linking import (
    link_documents_to_model,
    link_documents_to_model_typed,
    link_email_workflow_documents,
)
from tests.workflows.e2e.utils import handle_workflow_failure, log_workflow_success

logger = logging.getLogger(__name__)


@pytest.mark.e2e
@pytest.mark.django_db(transaction=True)
class TestPOCreationE2E:
    """End-to-end tests for PO Creation workflow using real activities."""

    @pytest.fixture
    def sample_po_email(self, fixture_path):
        """Load sample PO email fixture."""
        with open(fixture_path / "sample_po_email.json") as f:
            return json.load(f)

    async def run_po_creation_workflow(
        self,
        temporal_env_with_search_attributes,
        test_data_generator,
        email_data=None,
        require_human_validation=False,
        team=None,
        workflow_config=None,
    ):
        """Helper to run PO Creation workflow with real activities."""

        # Create test scenario if not provided
        if not team:
            scenario = await test_data_generator.create_complete_po_scenario(
                require_human_validation=require_human_validation
            )
            team = scenario["team"]
            workflow = scenario["workflow"]
        else:
            workflow = workflow_config

        # Create email if data provided
        email = None
        if email_data:
            # Create a supplier that matches the one in the email if needed
            from asgiref.sync import sync_to_async

            # Extract supplier name from email if present
            email_body = email_data.get("body", "")
            supplier_name = None
            if "Supplier:" in email_body:
                # Extract supplier name from "Supplier: Acme Supplies Inc" line
                for line in email_body.split("\n"):
                    if line.startswith("Supplier:"):
                        supplier_name = line.replace("Supplier:", "").strip()
                        break

            # Create or get supplier with matching name
            if supplier_name:
                # First try to find existing supplier
                from didero.suppliers.models import Supplier

                existing_supplier = await sync_to_async(
                    lambda: Supplier.objects.filter(
                        team=team, name=supplier_name
                    ).first()
                )()

                if existing_supplier:
                    supplier = existing_supplier
                else:
                    supplier = await test_data_generator.create_supplier(
                        team=team,
                        name=supplier_name,
                        email=email_data.get("recipient", "<EMAIL>"),
                    )
            else:
                # Use existing supplier from scenario
                suppliers = await sync_to_async(lambda: list(team.suppliers.all()))()
                supplier = suppliers[0] if suppliers else None

            email = await test_data_generator.create_email_with_attachment(
                team=team,
                subject=email_data["subject"],
                body=email_data["body"],
                sender=email_data.get("sender", "<EMAIL>"),
                supplier=supplier,
            )

        # Set up workflow parameters
        params = POCreationParams(
            team_id=str(team.id),
            workflow_id=str(workflow.id) if workflow else None,
            email_id=str(email.id) if email else None,
            # Note: po_number is NOT provided - this should trigger email-based flow
            # where AI extracts PO number, ERP fails, and AI fallback is used
        )

        # Start worker with REAL activities and executor for sync activities
        async with Worker(
            temporal_env_with_search_attributes.client,
            task_queue="e2e-po-creation",
            workflows=[POCreationWorkflow],
            activities=[
                # Real extraction activities
                extract_po_details,
                extract_po_from_erp,
                # Real creation activities
                create_po,
                create_po_confirmation_task,
                # Real configuration
                load_workflow_config,
                # Real linking activities
                link_email_workflow_documents,
                link_documents_to_model,
                link_documents_to_model_typed,
            ],
            activity_executor=ThreadPoolExecutor(
                max_workers=10
            ),  # Required for sync activities
        ):
            # Execute the workflow
            handle = await temporal_env_with_search_attributes.client.start_workflow(
                POCreationWorkflow.run,
                args=["e2e-test-workflow", params],
                id=f"e2e-po-creation-{datetime.now().isoformat()}",
                task_queue="e2e-po-creation",
                run_timeout=timedelta(minutes=5),
            )

            # Get the result with detailed error handling
            try:
                result = await handle.result()
                return result
            except Exception:
                await handle_workflow_failure(handle, "PO Creation")
                raise

    @pytest.mark.asyncio
    async def test_po_created_from_email_body(
        self, temporal_env_with_search_attributes, test_data_generator, sample_po_email
    ):
        """Test: PO is created from email body content."""
        # Run workflow with sample email
        result = await self.run_po_creation_workflow(
            temporal_env_with_search_attributes,
            test_data_generator,
            email_data=sample_po_email,
        )

        # Verify workflow succeeded
        assert result["success"] is True
        assert result["po_id"] is not None
        assert result["po_number"] is not None

        # Verify PO was created in database
        po = await sync_to_async(PurchaseOrder.objects.get)(id=result["po_id"])
        assert po.po_number == "PO-2024-001"  # From email body
        assert (
            po.order_status == "draft"
        )  # Created in draft due to human validation enabled

        # Verify line items were created
        items = await sync_to_async(lambda: list(po.items.all()))()
        assert len(items) == 3  # 3 items from email

        # Verify amounts
        total = sum(item.quantity * item.price for item in items)
        assert total.amount == Decimal("700.00")  # From email

    @pytest.mark.asyncio
    async def test_po_created_in_draft_with_human_validation(
        self, temporal_env_with_search_attributes, test_data_generator
    ):
        """Test: PO is created in DRAFT status when human validation is required."""
        # Create email with PO data (consistent with successful first test format)
        email_data = {
            "subject": "Purchase Order PO-DRAFT-001 - Test Supplier Inc",
            "body": "Dear Test Supplier Inc,\n\nPlease find our purchase order details below:\n\nPO Number: PO-DRAFT-001\nSupplier: Test Supplier Inc\nDate: 2024-01-15\n\nItems:\n1. Test Product - Item #TST-001 - Qty: 5 - Unit Price: $100.00 - Total: $500.00\n\nTotal Amount: $500.00\nCurrency: USD\n\nShip To:\n123 Test Street\nTest City, CA 90210\n\nPayment Terms: Net 30\n\nPlease confirm receipt of this order.\n\nBest regards,\nPurchasing Team",
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "attachments": [],
        }

        # Run workflow with human validation enabled
        result = await self.run_po_creation_workflow(
            temporal_env_with_search_attributes,
            test_data_generator,
            email_data=email_data,
            require_human_validation=True,
        )

        # Debug the result first
        log_workflow_success("PO Creation", result)

        # Verify workflow succeeded
        assert result["success"] is True, f"Workflow failed: {result}"

        # Check if PO ID is valid before proceeding
        po_id = result["po_id"]
        if po_id is None or po_id == "None":
            assert False, f"Workflow did not create a PO. Result: {result}"

        assert (
            result.get("created_in_draft") is True
        ), f"Expected created_in_draft=True but got: {result.get('created_in_draft')}"

        # Verify PO was created in DRAFT status
        po = await sync_to_async(PurchaseOrder.objects.get)(id=po_id)
        assert po.order_status == "draft"
        assert po.po_number == "PO-DRAFT-001"

    @pytest.mark.asyncio
    async def test_po_supplier_matching(
        self, temporal_env_with_search_attributes, test_data_generator
    ):
        """Test: Supplier is correctly matched based on email sender."""
        scenario = await test_data_generator.create_complete_po_scenario()

        # Create known supplier with specific email
        supplier = await test_data_generator.create_supplier(
            team=scenario["team"],
            name="Known Supplier Inc",
            email="<EMAIL>",
        )

        # Send email from supplier's email
        email_data = {
            "subject": "Purchase Order PO-SUPPLIER-001 - Known Supplier Inc",
            "body": "Dear Known Supplier Inc,\n\nPlease find our purchase order details below:\n\nPO Number: PO-SUPPLIER-001\nSupplier: Known Supplier Inc\nDate: 2024-01-15\n\nItems:\n1. Widget - Item #WDG-001 - Qty: 10 - Unit Price: $25.00 - Total: $250.00\n\nTotal Amount: $250.00\nCurrency: USD\n\nShip To:\n123 Main Street\nAnytown, CA 90210\n\nPayment Terms: Net 30\n\nPlease confirm receipt of this order.\n\nBest regards,\nPurchasing Team",
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "attachments": [],
        }

        result = await self.run_po_creation_workflow(
            temporal_env_with_search_attributes,
            test_data_generator,
            email_data=email_data,
            team=scenario["team"],
            workflow_config=scenario["workflow"],
        )

        # Verify PO is linked to correct supplier
        po = await sync_to_async(PurchaseOrder.objects.get)(id=result["po_id"])
        supplier = await sync_to_async(lambda: po.supplier)()
        assert supplier is not None
        supplier_name = await sync_to_async(lambda: supplier.name)()
        assert supplier_name == "Known Supplier Inc"
