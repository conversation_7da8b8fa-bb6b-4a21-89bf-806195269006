"""
E2E tests for Invoice Processing workflow.
Tests complete invoice processing flow from email to document creation.
"""

import logging
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

import pytest
from asgiref.sync import sync_to_async
from temporalio.worker import Worker

from didero.workflows.core_workflows.invoice_processing.workflow import (
    InvoiceProcessingWorkflow,
)
from tests.workflows.e2e.utils import log_workflow_success

logger = logging.getLogger(__name__)


@pytest.mark.e2e
@pytest.mark.django_db(transaction=True)
class TestInvoiceProcessingE2E:
    """End-to-end tests for Invoice Processing workflow using real activities."""

    @pytest.fixture
    def sample_invoice_email(self, fixture_path):
        """Load sample invoice email fixture."""
        import json

        with open(fixture_path / "sample_invoice_email.json") as f:
            return json.load(f)

    @pytest.fixture
    def sample_invoice_email_existing_po(self, fixture_path):
        """Load sample invoice email fixture for existing PO test."""
        import json

        with open(fixture_path / "sample_invoice_email_existing_po.json") as f:
            return json.load(f)

    async def run_invoice_processing_workflow(
        self,
        temporal_env,
        test_data_generator,
        email_data=None,
        team=None,
        workflow_config=None,
        existing_po=None,
    ):
        """Helper to run Invoice Processing workflow with real activities."""

        # Create test scenario if not provided
        if not team:
            scenario = await test_data_generator.create_complete_invoice_scenario(
                team_name="E2E Invoice Processing Test Team"
            )
            team = scenario["team"]
            workflow = scenario["workflow"]

            # For happy path test, disable validation to ensure "completed" status
            if not existing_po:  # Only for the first happy path test
                await test_data_generator.update_workflow_config(
                    workflow=workflow,
                    validate_invoice_amounts=False,
                )
        else:
            workflow = workflow_config

        # Create or use existing PO for the invoice to reference
        po = existing_po
        if not po:
            # Create items that will match the invoice
            items = (
                scenario.get("items", [])
                if not team
                else await test_data_generator.create_items(team, count=3)
            )

            # Create a PO that matches the invoice data exactly
            po = await test_data_generator.create_purchase_order_with_items(
                team=team,
                supplier=scenario["supplier"] if not team else None,
                user=scenario["user"] if not team else None,
                po_number="PO-INV-001",  # This should match the email
                status="issued",
                items=[
                    {"item": items[0], "quantity": 10, "unit_price": "25.00"},
                    {"item": items[1], "quantity": 20, "unit_price": "15.00"},
                    {"item": items[2], "quantity": 30, "unit_price": "10.00"},
                ],
            )

        # Create email if data provided
        email = None
        if email_data:
            # Create or get supplier with matching name
            from didero.suppliers.models import Supplier

            # Extract supplier name from email if present
            email_body = email_data.get("body", "")
            supplier_name = None
            if "Test Supplier Inc" in email_body:
                supplier_name = "Test Supplier Inc"

            # Create or get supplier with matching name
            if supplier_name:
                existing_supplier = await sync_to_async(
                    lambda: Supplier.objects.filter(
                        team=team, name=supplier_name
                    ).first()
                )()

                if existing_supplier:
                    supplier = existing_supplier
                else:
                    supplier = await test_data_generator.create_supplier(
                        team=team,
                        name=supplier_name,
                        email=email_data.get("sender", "<EMAIL>"),
                    )
            else:
                # Use existing supplier from scenario or PO
                supplier = po.supplier if po else scenario["supplier"]

            email = await test_data_generator.create_email_with_attachment(
                team=team,
                subject=email_data["subject"],
                body=email_data["body"],
                sender=email_data.get("sender", "<EMAIL>"),
                supplier=supplier,
            )

        # Set up workflow parameters (similar to other document workflows)
        params = {
            "email_id": str(email.id) if email else None,
            "team_id": str(team.id),
            "workflow_id": str(workflow.id) if workflow else None,
        }

        # Import ALL required activities for invoice processing workflow
        from didero.workflows.core_workflows.activities import (
            load_workflow_config,
        )
        from didero.workflows.core_workflows.invoice_processing.activities import (
            store_invoice_activity,
        )
        from didero.workflows.shared_activities.document_linking import (
            link_email_workflow_documents,
            link_model_to_purchase_order,
        )
        from didero.workflows.shared_activities.document_matching import (
            create_document_match_review_task_activity,
            match_documents_activity,
            skip_invoice_validation_activity,
        )
        from didero.workflows.shared_activities.document_retrieval import (
            retrieve_document_activity,
        )
        from didero.workflows.shared_activities.invoice_operations import (
            extract_invoice_details,
        )
        from didero.workflows.shared_activities.purchase_order_operations import (
            get_purchase_order_details_activity,
            get_purchase_order_id_from_po_number_activity,
        )

        # Start worker with ALL activities
        async with Worker(
            temporal_env.client,
            task_queue="e2e-invoice-processing",
            workflows=[InvoiceProcessingWorkflow],
            activities=[
                # Configuration (always needed first)
                load_workflow_config,
                # Document retrieval
                retrieve_document_activity,
                # Invoice processing
                extract_invoice_details,
                store_invoice_activity,
                # Purchase order operations
                get_purchase_order_details_activity,
                get_purchase_order_id_from_po_number_activity,
                # Email and document linking
                link_email_workflow_documents,
                link_model_to_purchase_order,
                # Document comparison (if enabled)
                match_documents_activity,
                skip_invoice_validation_activity,
                # Task creation for review
                create_document_match_review_task_activity,
            ],
            activity_executor=ThreadPoolExecutor(max_workers=10),
        ):
            # Execute the workflow
            handle = await temporal_env.client.start_workflow(
                InvoiceProcessingWorkflow.run,
                args=[str(workflow.id), params],  # workflow_id, params
                id=f"e2e-invoice-processing-{datetime.now().isoformat()}",
                task_queue="e2e-invoice-processing",
                run_timeout=timedelta(minutes=3),
            )

            # Get the result
            result = await handle.result()
            return result

    @pytest.mark.asyncio
    async def test_invoice_processing_happy_path(
        self, temporal_env, test_data_generator, sample_invoice_email
    ):
        """Test basic invoice processing end-to-end."""

        # Run workflow with sample email
        result = await self.run_invoice_processing_workflow(
            temporal_env,
            test_data_generator,
            email_data=sample_invoice_email,
        )

        # Verify workflow succeeded
        assert result["success"] is True
        assert result["invoice_id"] is not None
        assert result["status"] == "completed"

        log_workflow_success("Invoice Processing", {"invoice_id": result["invoice_id"]})
