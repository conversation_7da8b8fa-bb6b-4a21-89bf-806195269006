"""Tests for the Django admin helpers that render evaluation results."""

import pytest
from django.contrib.admin.sites import AdminSite

from didero.ai.admin import EvaluationResultAdmin
from didero.ai.models import EvaluationResult, EvaluationRun


@pytest.mark.django_db
def test_formatted_additional_actions_displays_workflow_summary():
    """The admin view should show matched workflows and the actual action names."""

    run = EvaluationRun.objects.create(dataset_name="hardcoded_basic")
    result = EvaluationResult.objects.create(
        run=run,
        test_name="shipment_notice",
        email_text="Sample email",
        expected_category="ORDER_SHIPPED",
        actual_output={
            "workflows_triggered": [
                {
                    "workflow_type": "shipment_processing",
                    "workflow_params": {},
                    "success": True,
                }
            ],
            "database_updates": [],
            "unhandled_cases": [],
        },
        additional_actions_found={
            "overall": {
                "matched": 1,
                "expected": 1,
                "missing": 0,
                "unexpected": 0,
            },
            "workflows": {
                "matched": 1,
                "expected": 1,
                "missing": 0,
                "unexpected": 0,
                "missing_items": [],
                "unexpected_items": [],
            },
            "database_updates": {
                "matched": 0,
                "expected": 0,
                "missing": 0,
                "unexpected": 0,
                "missing_items": [],
                "unexpected_items": [],
            },
            "human_tasks": {
                "matched": 0,
                "expected": 0,
                "missing": 0,
                "unexpected": 0,
                "missing_items": [],
                "unexpected_items": [],
            },
        },
        passed=True,
        score=1.0,
    )

    admin = EvaluationResultAdmin(EvaluationResult, AdminSite())

    assert admin.has_additional_actions(result) is True

    rendered = admin.formatted_additional_actions(result)
    assert "Workflows" in rendered
    assert "1/1 matched" in rendered
    assert "shipment_processing" in rendered
    assert "✅ Success" in rendered
    assert "<table" in rendered


@pytest.mark.django_db
def test_formatted_additional_actions_shows_missing_items():
    """Missing human tasks should render with their item names."""

    run = EvaluationRun.objects.create(dataset_name="hardcoded_basic")
    result = EvaluationResult.objects.create(
        run=run,
        test_name="unhandled_edge",
        email_text="Edge case",
        expected_category="OTHER",
        actual_output={
            "workflows_triggered": [],
            "database_updates": [],
            "unhandled_cases": [],
        },
        additional_actions_found={
            "overall": {
                "matched": 0,
                "expected": 1,
                "missing": 1,
                "unexpected": 0,
            },
            "human_tasks": {
                "matched": 0,
                "expected": 1,
                "missing": 1,
                "unexpected": 0,
                "missing_items": ["Clarify expedited order request"],
                "unexpected_items": [],
            },
            "workflows": {
                "matched": 0,
                "expected": 0,
                "missing": 0,
                "unexpected": 0,
                "missing_items": [],
                "unexpected_items": [],
            },
            "database_updates": {
                "matched": 0,
                "expected": 0,
                "missing": 0,
                "unexpected": 0,
                "missing_items": [],
                "unexpected_items": [],
            },
        },
        passed=False,
        score=0.0,
    )

    admin = EvaluationResultAdmin(EvaluationResult, AdminSite())

    assert admin.has_additional_actions(result) is True

    rendered = admin.formatted_additional_actions(result)
    assert "Human Tasks" in rendered
    assert "Missing" in rendered
    assert "Clarify expedited order request" in rendered
